-- CreateTable
CREATE TABLE "public"."wallet" (
    "id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "chain_name" TEXT NOT NULL,
    "wallet_address" TEXT NOT NULL,
    "is_primary" BOOLEAN NOT NULL DEFAULT false,
    "status" "public"."Status" NOT NULL DEFAULT 'ACTIVE',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "wallet_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."chain" (
    "id" UUID NOT NULL,
    "chain_id" TEXT NOT NULL,
    "chain_name" TEXT NOT NULL,
    "wallet_id" UUID NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "chain_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "wallet_user_id_idx" ON "public"."wallet"("user_id");

-- CreateIndex
CREATE INDEX "chain_wallet_id_idx" ON "public"."chain"("wallet_id");

-- AddForeignKey
ALTER TABLE "public"."wallet" ADD CONSTRAINT "wallet_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."chain" ADD CONSTRAINT "chain_wallet_id_fkey" FOREIGN KEY ("wallet_id") REFERENCES "public"."wallet"("id") ON DELETE CASCADE ON UPDATE CASCADE;
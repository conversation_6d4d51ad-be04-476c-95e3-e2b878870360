generator client {
  provider = "prisma-client-js"
  output   = "../generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                 String               @id @default(uuid(7)) @db.Uuid
  email              String               @unique
  username           String               @unique
  password           String
  type               UserType
  businessType       BusinessType?
  status             Status               @default(PENDING)
  createdAt          DateTime             @default(now()) @map("created_at")
  updatedAt          DateTime             @updatedAt @map("updated_at")
  firstName          String               @map("first_name")
  lastName           String               @map("last_name")
  googleId           String?              @unique
  oauthAccessToken   String?
  oauthProvider      String?
  lastLoginAt        DateTime?            @map("last_login_at")
  tokenVersion       Int                  @default(0) @map("token_version")
  applicantId        String?              @map("applicant_id")
  ActivationToken    ActivationToken[]
  kyb                Kyb?
  kyc                Kyc?
  PasswordResetToken PasswordResetToken[]
  refreshTokens      RefreshToken[]
  wallets            Wallet[]

  @@map("user")
}

model Kyc {
  id             String       @id @default(uuid(7)) @db.Uuid
  identityType   IdentityType @map("identity_type")
  identityNumber String       @map("identity_number")
  birthdate      DateTime
  birthplace     String
  address        String
  state          String
  country        String
  zipNumber      String       @map("zip_number")
  phoneNumber    String       @map("phone_number")
  createdAt      DateTime     @default(now()) @map("created_at")
  updatedAt      DateTime     @updatedAt @map("updated_at")
  userId         String       @unique @map("user_id") @db.Uuid
  provider       String?
  fileName       String?      @map("file_name")
  status         KycStatus    @default(PENDING)
  user           User         @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("kyc")
}

model Kyb {
  id                 String       @id @default(uuid(7)) @db.Uuid
  namaPerusahaan     String       @map("nama_perusahaan")
  address            String
  ubo                String
  contactInformation String       @map("contact_information")
  legalRegistration  String       @map("legal_registration")
  registrationNumber String       @map("registration_number")
  kybStatus          KybStatus    @default(PENDING) @map("kyb_status")
  userId             String       @unique @map("user_id") @db.Uuid
  createdAt          DateTime     @default(now()) @map("created_at")
  updatedAt          DateTime     @updatedAt @map("updated_at")
  provider           String?
  user               User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  uboProfiles        UboProfile[]

  @@map("kyb")
}

model UboProfile {
  id              String       @id @default(uuid(7)) @db.Uuid
  identityType    IdentityType @map("identity_type")
  identityNumber  String       @map("identity_number")
  firstName       String       @map("first_name")
  lastName        String       @map("last_name")
  address         String
  telephoneNumber String       @map("telephone_number")
  kybId           String       @map("kyb_id") @db.Uuid
  createdAt       DateTime     @default(now()) @map("created_at")
  updatedAt       DateTime     @updatedAt @map("updated_at")
  kyb             Kyb          @relation(fields: [kybId], references: [id], onDelete: Cascade)

  @@map("ubo_profile")
}

model Role {
  id        String   @id @default(uuid(7)) @db.Uuid
  name      String   @unique
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  admins    Admin[]

  @@map("role")
}

model Admin {
  id        String   @id @default(uuid(7)) @db.Uuid
  email     String   @unique
  username  String   @unique
  password  String
  roleId    String   @map("role_id") @db.Uuid
  status    Status   @default(ACTIVE)
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  role      Role     @relation(fields: [roleId], references: [id])
  logs      Log[]

  @@map("admin")
}

model Log {
  id        String   @id @default(uuid(7)) @db.Uuid
  actions   String
  timestamp DateTime @default(now())
  message   String?
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  adminId   String?  @map("admin_id") @db.Uuid
  admin     Admin?   @relation(fields: [adminId], references: [id])

  @@map("log")
}

model ActivationToken {
  id        String   @id @default(uuid(7)) @db.Uuid
  token     String   @unique
  userId    String   @map("user_id") @db.Uuid
  expiresAt DateTime @map("expires_at")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("activation_token")
}

model PasswordResetToken {
  id        String   @id @default(uuid(7)) @db.Uuid
  token     String   @unique
  userId    String   @map("user_id") @db.Uuid
  expiresAt DateTime @map("expires_at")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("password_reset_token")
}

model RefreshToken {
  id         String    @id @default(uuid(7)) @db.Uuid
  token      String    @unique
  userId     String    @map("user_id") @db.Uuid
  deviceId   String?   @map("device_id")
  userAgent  String?   @map("user_agent")
  ipAddress  String?   @map("ip_address")
  isRevoked  Boolean   @default(false) @map("is_revoked")
  expiresAt  DateTime  @map("expires_at")
  createdAt  DateTime  @default(now()) @map("created_at")
  updatedAt  DateTime  @updatedAt @map("updated_at")
  lastUsedAt DateTime? @map("last_used_at")
  user       User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId, isRevoked])
  @@index([token, expiresAt])
  @@map("refresh_token")
}

model AccessTokenBlacklist {
  id        String   @id @default(uuid(7)) @db.Uuid
  tokenHash String   @unique @map("token_hash")
  userId    String   @map("user_id") @db.Uuid
  expiresAt DateTime @map("expires_at")
  revokedAt DateTime @default(now()) @map("revoked_at")
  reason    String?

  @@index([tokenHash, expiresAt])
  @@map("access_token_blacklist")
}

model NavManagement {
  id          String    @id @default(uuid(7)) @db.Uuid
  date        DateTime
  price       Decimal   @db.Decimal(10, 2)
  status      NavStatus @default(WAITING_FOR_APPROVAL)
  notes       String?
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")
  safeTxHash  String?   @map("safe_tx_hash")
  signature   String?
  manager_nav String?
  product_id  String?   @db.Uuid
  product     Product?  @relation(fields: [product_id], references: [id])

  @@map("nav_management")
}

model Product {
  id                String              @id @default(uuid(7)) @db.Uuid
  displayName       String              @map("display_name")
  tokenName         String              @map("token_name")
  underlyingName    String              @map("underlying_name")
  symbol            String
  img               String?
  description       String?
  price             Decimal             @db.Decimal(18, 8)
  high              Decimal?            @db.Decimal(18, 8)
  low               Decimal?            @db.Decimal(18, 8)
  priceChange24h    Decimal             @map("price_change_24h") @db.Decimal(18, 8)
  priceChangePct24h Decimal             @map("price_change_pct_24h") @db.Decimal(10, 4)
  apy               Decimal?            @db.Decimal(10, 4)
  tvl               Decimal?            @db.Decimal(18, 8)
  createdAt         DateTime            @default(now()) @map("created_at")
  updatedAt         DateTime?           @map("updated_at")
  nav_management    NavManagement[]
  productTags       ProductTag[]
  supportedNetworks supported_network[]

  @@map("product")
}

model supported_network {
  id          String   @id @default(uuid(7)) @db.Uuid
  networkName String   @map("network_name")
  networkCode String   @map("network_code")
  chainId     Int      @map("chain_id")
  address     String
  decimals    Int
  productId   String   @map("product_id") @db.Uuid
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")
  img         String?
  product     Product  @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("supported_network")
}

model Category {
  slug        String   @unique
  label       String
  layer       String
  description String?
  isActive    Boolean  @default(true) @map("is_active")
  sortOrder   Int?     @map("sort_order")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")
  id          Int      @id @default(autoincrement())
  tags        Tag[]

  @@index([layer, sortOrder])
  @@index([slug])
  @@map("category")
}

model Tag {
  slug        String       @unique
  label       String
  description String?
  isActive    Boolean      @default(true) @map("is_active")
  sortOrder   Int?         @map("sort_order")
  createdAt   DateTime     @default(now()) @map("created_at")
  updatedAt   DateTime     @updatedAt @map("updated_at")
  id          Int          @id @default(autoincrement())
  categoryId  Int          @map("category_id")
  productTags ProductTag[]
  category    Category     @relation(fields: [categoryId], references: [id], onDelete: Cascade)

  @@index([categoryId])
  @@index([slug])
  @@map("tag")
}

model ProductTag {
  id        String   @id @default(uuid(7)) @db.Uuid
  productId String   @map("product_id") @db.Uuid
  createdAt DateTime @default(now()) @map("created_at")
  tagId     Int      @map("tag_id")
  product   Product  @relation(fields: [productId], references: [id], onDelete: Cascade)
  tag       Tag      @relation(fields: [tagId], references: [id], onDelete: Cascade)

  @@unique([productId, tagId])
  @@index([productId])
  @@index([tagId])
  @@map("product_tag")
}

model Wallet {
  id            String     @id @default(uuid(7)) @db.Uuid
  userId        String     @map("user_id") @db.Uuid
  walletAddress String     @map("wallet_address")
  isPrimary     Boolean    @default(false) @map("is_primary")
  status        Status     @default(ACTIVE)
  createdAt     DateTime   @default(now()) @map("created_at")
  walletType    WalletType @map("wallet_type")
  chains        Chain[]
  user          User       @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("wallet")
}

model Chain {
  id        String      @id @default(uuid(7)) @db.Uuid
  chainId   String      @map("chain_id")
  chainName String      @map("chain_name")
  walletId  String      @map("wallet_id") @db.Uuid
  createdAt DateTime    @default(now()) @map("created_at")
  symbol    ChainSymbol @map("symbol")
  wallet    Wallet      @relation(fields: [walletId], references: [id], onDelete: Cascade)

  @@map("chain")
}

enum UserType {
  CONSUMER
  BUSINESS
}

enum BusinessType {
  RETAIL
  MERCHANT
}

enum Status {
  ACTIVE
  PENDING
  FREEZE
  DEACTIVE
}

enum KycStatus {
  PENDING
  APPROVED
  REJECTED
  DENIED
  CANCELED
  MANUAL_REVIEW
  LOCKED
}

enum KybStatus {
  PENDING
  APPROVED
  REJECTED
  DENIED
  CANCELED
  MANUAL_REVIEW
  LOCKED
}

enum IdentityType {
  KTP
  PASSPORT
  DRIVER_LICENSE
  SIM
}

enum NavStatus {
  WAITING_FOR_APPROVAL
  WAITING_FOR_POSTING
  POSTING_IN_PROCESS
  POSTED
  REJECTED
  POSTING_ERROR
}

enum ChainSymbol {
  ETH
  BNB
  MATIC
  AVAX
  FTM
  ARB
  OP
}

enum WalletType {
  METAMASK
  WALLET_CONNECT
  SAFE
  EXTERNAL
  HARDWARE
}

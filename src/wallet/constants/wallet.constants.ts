import { WalletType, ChainSymbol } from '../../../generated/prisma';
import { AddressValidation, SupportedNetwork } from '../interfaces/wallet.interface';

/**
 * Valid wallet statuses
 */
export const VALID_WALLET_STATUSES = ['ACTIVE', 'PENDING', 'FREEZE', 'DEACTIVE'] as const;

/**
 * Valid user statuses for wallet creation
 */
export const VALID_USER_STATUSES_FOR_WALLET = ['ACTIVE'] as const;

/**
 * Maximum number of wallets per user
 */
export const MAX_WALLETS_PER_USER = 10;

/**
 * Wallet status messages
 */
export const WALLET_STATUS_MESSAGES = {
  ACTIVE: 'Wallet is active and ready to use',
  PENDING: 'Wallet is pending verification',
  FREEZE: 'Wallet is temporarily frozen',
  DEACTIVE: 'Wallet has been deactivated',
} as const;

/**
 * Error messages for wallet operations
 */
export const WALLET_ERROR_MESSAGES = {
  INVALID_USER_ID: 'Invalid user ID provided',
  USER_NOT_FOUND: 'User with the specified ID not found',
  USER_NOT_ACTIVE: 'User account must be active to create wallets',
  WALLET_ADDRESS_EXISTS: 'Wallet address is already registered in the system',
  WALLET_NOT_FOUND: 'Wallet not found or access denied',
  INVALID_WALLET_ADDRESS: 'Wallet address must be a valid format',
  INVALID_WALLET_TYPE: 'Invalid wallet type provided',
  INVALID_CHAIN_SYMBOL: 'Invalid chain symbol provided',
  MAX_WALLETS_EXCEEDED: `Maximum number of wallets per user (${MAX_WALLETS_PER_USER}) exceeded`,
  CHAIN_INFO_MISSING: 'Wallet chain information is missing',
  TRANSACTION_FAILED: 'Database transaction failed',
  UNEXPECTED_ERROR: 'An unexpected error occurred',
  // Multiple chains specific errors
  NO_CHAINS_PROVIDED: 'At least one chain must be provided',
  TOO_MANY_CHAINS: 'Maximum of 10 chains allowed per wallet',
  DUPLICATE_CHAINS: 'Duplicate chains are not allowed for the same wallet',
  INCOMPATIBLE_CHAIN_WALLET: 'Chain is not compatible with the selected wallet type',
  CHAIN_CREATION_FAILED: 'Failed to create one or more chains for the wallet',
} as const;

/**
 * Success messages for wallet operations
 */
export const WALLET_SUCCESS_MESSAGES = {
  WALLET_CREATED: 'Wallet successfully created',
  WALLET_RETRIEVED: 'Wallet information retrieved successfully',
  WALLETS_RETRIEVED: 'User wallets retrieved successfully',
  WALLET_UPDATED: 'Wallet updated successfully',
  WALLET_DELETED: 'Wallet deleted successfully',
} as const;

/**
 * Supported wallet types with descriptions
 */
export const SUPPORTED_WALLET_TYPES = {
  [WalletType.METAMASK]: {
    name: 'MetaMask',
    description: 'Browser extension and mobile wallet',
    website: 'https://metamask.io',
  },
  [WalletType.WALLET_CONNECT]: {
    name: 'WalletConnect',
    description: 'Protocol for connecting wallets to dApps',
    website: 'https://walletconnect.com',
  },
  [WalletType.SAFE]: {
    name: 'Safe (Gnosis Safe)',
    description: 'Multi-signature smart contract wallet',
    website: 'https://safe.global',
  },
  [WalletType.EXTERNAL]: {
    name: 'External Wallet',
    description: 'External wallet connections',
    website: '',
  },
  [WalletType.HARDWARE]: {
    name: 'Hardware Wallet',
    description: 'Physical hardware security devices',
    website: '',
  },
} as const;

/**
 * Address validation patterns for different blockchain networks
 */
export const ADDRESS_VALIDATIONS: Record<string, AddressValidation> = {
  ETHEREUM: {
    format: /^0x[a-fA-F0-9]{40}$/,
    name: 'Ethereum',
    description: 'Ethereum address format (0x followed by 40 hexadecimal characters)',
    examples: ['******************************************'],
  },
  BITCOIN: {
    format: /^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$|^bc1[a-z0-9]{39,59}$/,
    name: 'Bitcoin',
    description: 'Bitcoin address format (Legacy, SegWit, or Bech32)',
    examples: ['**********************************', '******************************************'],
  },
  SOLANA: {
    format: /^[1-9A-HJ-NP-Za-km-z]{32,44}$/,
    name: 'Solana',
    description: 'Solana address format (Base58 encoded)',
    examples: ['********************************'],
  },
} as const;

/**
 * Supported blockchain networks configuration
 */
export const SUPPORTED_NETWORKS: Record<ChainSymbol, SupportedNetwork> = {
  [ChainSymbol.ETH]: {
    chainId: '1',
    chainName: 'Ethereum Mainnet',
    symbol: ChainSymbol.ETH,
    isMainnet: true,
    rpcUrls: ['https://mainnet.infura.io/v3/', 'https://eth-mainnet.alchemyapi.io/v2/'],
    blockExplorerUrls: ['https://etherscan.io'],
    nativeCurrency: {
      name: 'Ether',
      symbol: 'ETH',
      decimals: 18,
    },
  },
  [ChainSymbol.BNB]: {
    chainId: '56',
    chainName: 'BNB Smart Chain',
    symbol: ChainSymbol.BNB,
    isMainnet: true,
    rpcUrls: ['https://bsc-dataseed.binance.org/'],
    blockExplorerUrls: ['https://bscscan.com'],
    nativeCurrency: {
      name: 'BNB',
      symbol: 'BNB',
      decimals: 18,
    },
  },
  [ChainSymbol.MATIC]: {
    chainId: '137',
    chainName: 'Polygon',
    symbol: ChainSymbol.MATIC,
    isMainnet: true,
    rpcUrls: ['https://polygon-rpc.com/'],
    blockExplorerUrls: ['https://polygonscan.com'],
    nativeCurrency: {
      name: 'MATIC',
      symbol: 'MATIC',
      decimals: 18,
    },
  },
  [ChainSymbol.AVAX]: {
    chainId: '43114',
    chainName: 'Avalanche C-Chain',
    symbol: ChainSymbol.AVAX,
    isMainnet: true,
    rpcUrls: ['https://api.avax.network/ext/bc/C/rpc'],
    blockExplorerUrls: ['https://snowtrace.io'],
    nativeCurrency: {
      name: 'Avalanche',
      symbol: 'AVAX',
      decimals: 18,
    },
  },
  [ChainSymbol.FTM]: {
    chainId: '250',
    chainName: 'Fantom Opera',
    symbol: ChainSymbol.FTM,
    isMainnet: true,
    rpcUrls: ['https://rpc.ftm.tools/'],
    blockExplorerUrls: ['https://ftmscan.com'],
    nativeCurrency: {
      name: 'Fantom',
      symbol: 'FTM',
      decimals: 18,
    },
  },
  [ChainSymbol.ARB]: {
    chainId: '42161',
    chainName: 'Arbitrum One',
    symbol: ChainSymbol.ARB,
    isMainnet: true,
    rpcUrls: ['https://arb1.arbitrum.io/rpc'],
    blockExplorerUrls: ['https://arbiscan.io'],
    nativeCurrency: {
      name: 'Arbitrum',
      symbol: 'ARB',
      decimals: 18,
    },
  },
  [ChainSymbol.OP]: {
    chainId: '10',
    chainName: 'Optimism',
    symbol: ChainSymbol.OP,
    isMainnet: true,
    rpcUrls: ['https://mainnet.optimism.io'],
    blockExplorerUrls: ['https://optimistic.etherscan.io'],
    nativeCurrency: {
      name: 'Optimism',
      symbol: 'OP',
      decimals: 18,
    },
  },
} as const;

/**
 * Default wallet configuration
 */
export const DEFAULT_WALLET_CONFIG = {
  businessRules: {
    maxWalletsPerUser: MAX_WALLETS_PER_USER,
    supportedWalletTypes: Object.values(WalletType),
    supportedChainSymbols: Object.values(ChainSymbol),
    allowedAddressFormats: Object.values(ADDRESS_VALIDATIONS).map(v => v.format),
    minAddressLength: 26,
    maxAddressLength: 62,
  },
  enableLogging: true,
  enableValidation: true,
  enablePrimaryWalletAutoAssignment: true,
} as const;

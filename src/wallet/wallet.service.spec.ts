import { Test, TestingModule } from '@nestjs/testing';
import { NotFoundException, BadRequestException } from '@nestjs/common';
import { WalletService } from './wallet.service';
import { PrismaService } from '../prisma/prisma.service';
import { UpdateWalletDto } from './dto/update-wallet.dto';
import { WalletType, ChainSymbol } from '../../generated/prisma';

describe('WalletService - Update and Delete', () => {
  let service: WalletService;
  let prismaService: PrismaService;

  const mockUser = {
    id: '01994ba2-3c6e-7b53-9660-0f766d409577',
    email: '<EMAIL>',
    status: 'ACTIVE',
  };

  const mockWallet = {
    id: '01994ba2-3c6e-7b53-9660-0f766d409578',
    userId: '01994ba2-3c6e-7b53-9660-0f766d409577',
    walletAddress: '******************************************',
    walletType: WalletType.METAMASK,
    isPrimary: true,
    status: 'ACTIVE',
    createdAt: new Date('2024-01-01'),
    chains: [
      {
        id: '01994ba2-3c6e-7b53-9660-0f766d409579',
        chainId: '1',
        chainName: 'Ethereum Mainnet',
        symbol: ChainSymbol.ETH,
        walletId: '01994ba2-3c6e-7b53-9660-0f766d409578',
        createdAt: new Date('2024-01-01'),
      },
    ],
  };

  const mockSecondaryWallet = {
    id: '01994ba2-3c6e-7b53-9660-0f766d409580',
    userId: '01994ba2-3c6e-7b53-9660-0f766d409577',
    walletAddress: '******************************************',
    walletType: WalletType.METAMASK,
    isPrimary: false,
    status: 'ACTIVE',
    createdAt: new Date('2024-01-02'),
    chains: [
      {
        id: '01994ba2-3c6e-7b53-9660-0f766d409581',
        chainId: '137',
        chainName: 'Polygon Mainnet',
        symbol: ChainSymbol.MATIC,
        walletId: '01994ba2-3c6e-7b53-9660-0f766d409580',
        createdAt: new Date('2024-01-02'),
      },
    ],
  };

  const mockPrismaService = {
    user: {
      findUnique: jest.fn(),
    },
    wallet: {
      findFirst: jest.fn(),
      findMany: jest.fn(),
      update: jest.fn(),
      updateMany: jest.fn(),
      delete: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WalletService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    service = module.get<WalletService>(WalletService);
    prismaService = module.get<PrismaService>(PrismaService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('updateWallet', () => {
    const updateDto: UpdateWalletDto = { is_primary: true };

    it('should update wallet successfully when setting as primary', async () => {
      mockPrismaService.user.findUnique.mockResolvedValue(mockUser);
      mockPrismaService.wallet.findFirst.mockResolvedValue(mockSecondaryWallet);
      mockPrismaService.wallet.updateMany.mockResolvedValue({ count: 1 });
      mockPrismaService.wallet.update.mockResolvedValue({
        ...mockSecondaryWallet,
        isPrimary: true,
      });

      const result = await service.updateWallet(
        '01994ba2-3c6e-7b53-9660-0f766d409580',
        '01994ba2-3c6e-7b53-9660-0f766d409577',
        updateDto,
      );

      expect(mockPrismaService.wallet.updateMany).toHaveBeenCalledWith({
        where: {
          userId: '01994ba2-3c6e-7b53-9660-0f766d409577',
          id: { not: '01994ba2-3c6e-7b53-9660-0f766d409580' },
        },
        data: {
          isPrimary: false,
        },
      });

      expect(mockPrismaService.wallet.update).toHaveBeenCalledWith({
        where: { id: '01994ba2-3c6e-7b53-9660-0f766d409580' },
        data: { isPrimary: true },
        include: { chains: true },
      });

      expect(result.is_primary).toBe(true);
      expect(result.id).toBe('01994ba2-3c6e-7b53-9660-0f766d409580');
    });

    it('should handle unsetting primary wallet and assign to oldest wallet', async () => {
      const unsetPrimaryDto: UpdateWalletDto = { is_primary: false };
      
      mockPrismaService.user.findUnique.mockResolvedValue(mockUser);
      mockPrismaService.wallet.findFirst
        .mockResolvedValueOnce(mockWallet) // Current wallet (primary)
        .mockResolvedValueOnce(mockSecondaryWallet); // Oldest remaining wallet
      mockPrismaService.wallet.update
        .mockResolvedValueOnce(mockSecondaryWallet) // Update oldest to primary
        .mockResolvedValueOnce({ ...mockWallet, isPrimary: false }); // Update current wallet

      const result = await service.updateWallet(
        '01994ba2-3c6e-7b53-9660-0f766d409578',
        '01994ba2-3c6e-7b53-9660-0f766d409577',
        unsetPrimaryDto,
      );

      expect(mockPrismaService.wallet.update).toHaveBeenCalledWith({
        where: { id: '01994ba2-3c6e-7b53-9660-0f766d409580' },
        data: { isPrimary: true },
      });

      expect(result.is_primary).toBe(false);
    });

    it('should throw NotFoundException when wallet not found', async () => {
      mockPrismaService.user.findUnique.mockResolvedValue(mockUser);
      mockPrismaService.wallet.findFirst.mockResolvedValue(null);

      await expect(
        service.updateWallet('01994ba2-3c6e-7b53-9660-0f766d409999', '01994ba2-3c6e-7b53-9660-0f766d409577', updateDto),
      ).rejects.toThrow(NotFoundException);
    });

    it('should throw NotFoundException when user not found', async () => {
      mockPrismaService.user.findUnique.mockResolvedValue(null);

      await expect(
        service.updateWallet('01994ba2-3c6e-7b53-9660-0f766d409578', '01994ba2-3c6e-7b53-9660-0f766d409999', updateDto),
      ).rejects.toThrow(NotFoundException);
    });

    it('should throw BadRequestException for invalid user ID format', async () => {
      await expect(
        service.updateWallet('01994ba2-3c6e-7b53-9660-0f766d409578', 'invalid-id', updateDto),
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe('deleteWallet', () => {
    it('should delete non-primary wallet successfully', async () => {
      mockPrismaService.user.findUnique.mockResolvedValue(mockUser);
      mockPrismaService.wallet.findFirst.mockResolvedValue(mockSecondaryWallet);
      mockPrismaService.wallet.delete.mockResolvedValue(mockSecondaryWallet);

      const result = await service.deleteWallet('01994ba2-3c6e-7b53-9660-0f766d409580', '01994ba2-3c6e-7b53-9660-0f766d409577');

      expect(mockPrismaService.wallet.delete).toHaveBeenCalledWith({
        where: { id: '01994ba2-3c6e-7b53-9660-0f766d409580' },
      });

      expect(result.message).toBe('Wallet deleted successfully');
    });

    it('should delete primary wallet and assign primary to next wallet', async () => {
      mockPrismaService.user.findUnique.mockResolvedValue(mockUser);
      mockPrismaService.wallet.findFirst
        .mockResolvedValueOnce(mockWallet) // Current wallet (primary)
        .mockResolvedValueOnce(mockSecondaryWallet); // Next wallet to become primary
      mockPrismaService.wallet.update.mockResolvedValue({
        ...mockSecondaryWallet,
        isPrimary: true,
      });
      mockPrismaService.wallet.delete.mockResolvedValue(mockWallet);

      const result = await service.deleteWallet('01994ba2-3c6e-7b53-9660-0f766d409578', '01994ba2-3c6e-7b53-9660-0f766d409577');

      expect(mockPrismaService.wallet.update).toHaveBeenCalledWith({
        where: { id: '01994ba2-3c6e-7b53-9660-0f766d409580' },
        data: { isPrimary: true },
      });

      expect(mockPrismaService.wallet.delete).toHaveBeenCalledWith({
        where: { id: '01994ba2-3c6e-7b53-9660-0f766d409578' },
      });

      expect(result.message).toBe('Wallet deleted successfully');
    });

    it('should throw NotFoundException when wallet not found', async () => {
      mockPrismaService.user.findUnique.mockResolvedValue(mockUser);
      mockPrismaService.wallet.findFirst.mockResolvedValue(null);

      await expect(
        service.deleteWallet('01994ba2-3c6e-7b53-9660-0f766d409999', '01994ba2-3c6e-7b53-9660-0f766d409577'),
      ).rejects.toThrow(NotFoundException);
    });

    it('should throw NotFoundException when user not found', async () => {
      mockPrismaService.user.findUnique.mockResolvedValue(null);

      await expect(
        service.deleteWallet('01994ba2-3c6e-7b53-9660-0f766d409578', '01994ba2-3c6e-7b53-9660-0f766d409999'),
      ).rejects.toThrow(NotFoundException);
    });

    it('should throw BadRequestException for invalid user ID format', async () => {
      await expect(
        service.deleteWallet('01994ba2-3c6e-7b53-9660-0f766d409578', 'invalid-id'),
      ).rejects.toThrow(BadRequestException);
    });
  });
});

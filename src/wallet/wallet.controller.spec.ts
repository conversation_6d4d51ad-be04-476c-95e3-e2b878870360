import { Test, TestingModule } from '@nestjs/testing';
import { NotFoundException } from '@nestjs/common';
import { WalletController } from './wallet.controller';
import { WalletService } from './wallet.service';
import { UpdateWalletDto } from './dto/update-wallet.dto';
import { WalletResponseDto } from './dto/wallet-response.dto';
import { AuthenticatedRequest } from '../kyc/interfaces/authenticated-user.interface';
import { WalletType, ChainSymbol } from '../../generated/prisma';
import { EnhancedJwtAuthGuard } from '../auth/guards/enhanced-jwt-auth.guard';

describe('WalletController - Update and Delete', () => {
  let controller: WalletController;
  let walletService: WalletService;

  const mockWalletResponse: WalletResponseDto = {
    id: '01994ba2-3c6e-7b53-9660-0f766d409578',
    user_id: '01994ba2-3c6e-7b53-9660-0f766d409577',
    wallet_address: '******************************************',
    wallet_type: WalletType.METAMASK,
    is_primary: true,
    status: 'ACTIVE',
    created_at: new Date('2024-01-01'),
    chains: [
      {
        id: '01994ba2-3c6e-7b53-9660-0f766d409579',
        chain_id: '1',
        chain_name: 'Ethereum Mainnet',
        symbol: ChainSymbol.ETH,
        wallet_id: '01994ba2-3c6e-7b53-9660-0f766d409578',
        created_at: new Date('2024-01-01'),
      },
    ],
  };

  const mockAuthenticatedRequest = {
    user: {
      id: '01994ba2-3c6e-7b53-9660-0f766d409577',
      email: '<EMAIL>',
      username: 'testuser',
      type: 'CONSUMER',
      status: 'ACTIVE',
      tokenVersion: 0,
    },
  } as AuthenticatedRequest;

  const mockWalletService = {
    updateWallet: jest.fn(),
    deleteWallet: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [WalletController],
      providers: [
        {
          provide: WalletService,
          useValue: mockWalletService,
        },
      ],
    })
      .overrideGuard(EnhancedJwtAuthGuard)
      .useValue({
        canActivate: jest.fn(() => true),
      })
      .compile();

    controller = module.get<WalletController>(WalletController);
    walletService = module.get<WalletService>(WalletService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('updateWallet', () => {
    const updateDto: UpdateWalletDto = { is_primary: true };

    it('should update wallet successfully', async () => {
      mockWalletService.updateWallet.mockResolvedValue(mockWalletResponse);

      const result = await controller.updateWallet(
        '01994ba2-3c6e-7b53-9660-0f766d409578',
        updateDto,
        mockAuthenticatedRequest,
      );

      expect(mockWalletService.updateWallet).toHaveBeenCalledWith(
        '01994ba2-3c6e-7b53-9660-0f766d409578',
        '01994ba2-3c6e-7b53-9660-0f766d409577',
        updateDto,
      );
      expect(result).toEqual(mockWalletResponse);
      expect(result.is_primary).toBe(true);
    });

    it('should handle wallet not found error', async () => {
      mockWalletService.updateWallet.mockRejectedValue(
        new NotFoundException('Wallet not found or access denied'),
      );

      await expect(
        controller.updateWallet(
          '01994ba2-3c6e-7b53-9660-0f766d409999',
          updateDto,
          mockAuthenticatedRequest,
        ),
      ).rejects.toThrow(NotFoundException);

      expect(mockWalletService.updateWallet).toHaveBeenCalledWith(
        '01994ba2-3c6e-7b53-9660-0f766d409999',
        '01994ba2-3c6e-7b53-9660-0f766d409577',
        updateDto,
      );
    });

    it('should update wallet to non-primary', async () => {
      const nonPrimaryDto: UpdateWalletDto = { is_primary: false };
      const nonPrimaryResponse = {
        ...mockWalletResponse,
        is_primary: false,
      };

      mockWalletService.updateWallet.mockResolvedValue(nonPrimaryResponse);

      const result = await controller.updateWallet(
        '01994ba2-3c6e-7b53-9660-0f766d409578',
        nonPrimaryDto,
        mockAuthenticatedRequest,
      );

      expect(mockWalletService.updateWallet).toHaveBeenCalledWith(
        '01994ba2-3c6e-7b53-9660-0f766d409578',
        '01994ba2-3c6e-7b53-9660-0f766d409577',
        nonPrimaryDto,
      );
      expect(result.is_primary).toBe(false);
    });

    it('should extract user ID correctly from request', async () => {
      mockWalletService.updateWallet.mockResolvedValue(mockWalletResponse);

      await controller.updateWallet(
        '01994ba2-3c6e-7b53-9660-0f766d409578',
        updateDto,
        mockAuthenticatedRequest,
      );

      expect(mockWalletService.updateWallet).toHaveBeenCalledWith(
        '01994ba2-3c6e-7b53-9660-0f766d409578',
        '01994ba2-3c6e-7b53-9660-0f766d409577', // Should extract this from the request
        updateDto,
      );
    });
  });

  describe('deleteWallet', () => {
    const deleteResponse = { message: 'Wallet deleted successfully' };

    it('should delete wallet successfully', async () => {
      mockWalletService.deleteWallet.mockResolvedValue(deleteResponse);

      const result = await controller.deleteWallet(
        '01994ba2-3c6e-7b53-9660-0f766d409578',
        mockAuthenticatedRequest,
      );

      expect(mockWalletService.deleteWallet).toHaveBeenCalledWith(
        '01994ba2-3c6e-7b53-9660-0f766d409578',
        '01994ba2-3c6e-7b53-9660-0f766d409577',
      );
      expect(result).toEqual(deleteResponse);
      expect(result.message).toBe('Wallet deleted successfully');
    });

    it('should handle wallet not found error', async () => {
      mockWalletService.deleteWallet.mockRejectedValue(
        new NotFoundException('Wallet not found or access denied'),
      );

      await expect(
        controller.deleteWallet('01994ba2-3c6e-7b53-9660-0f766d409999', mockAuthenticatedRequest),
      ).rejects.toThrow(NotFoundException);

      expect(mockWalletService.deleteWallet).toHaveBeenCalledWith(
        '01994ba2-3c6e-7b53-9660-0f766d409999',
        '01994ba2-3c6e-7b53-9660-0f766d409577',
      );
    });

    it('should extract user ID correctly from request', async () => {
      mockWalletService.deleteWallet.mockResolvedValue(deleteResponse);

      await controller.deleteWallet('01994ba2-3c6e-7b53-9660-0f766d409578', mockAuthenticatedRequest);

      expect(mockWalletService.deleteWallet).toHaveBeenCalledWith(
        '01994ba2-3c6e-7b53-9660-0f766d409578',
        '01994ba2-3c6e-7b53-9660-0f766d409577', // Should extract this from the request
      );
    });

    it('should handle service errors gracefully', async () => {
      const serviceError = new Error('Database connection failed');
      mockWalletService.deleteWallet.mockRejectedValue(serviceError);

      await expect(
        controller.deleteWallet('01994ba2-3c6e-7b53-9660-0f766d409578', mockAuthenticatedRequest),
      ).rejects.toThrow('Database connection failed');
    });
  });

  describe('error handling and logging', () => {
    it('should log update operations', async () => {
      const logSpy = jest.spyOn(controller['logger'], 'log');
      mockWalletService.updateWallet.mockResolvedValue(mockWalletResponse);

      await controller.updateWallet(
        '01994ba2-3c6e-7b53-9660-0f766d409578',
        { is_primary: true },
        mockAuthenticatedRequest,
      );

      expect(logSpy).toHaveBeenCalledWith(
        'Updating wallet 01994ba2-3c6e-7b53-9660-0f766d409578 for user: 01994ba2-3c6e-7b53-9660-0f766d409577',
      );
    });

    it('should log delete operations', async () => {
      const logSpy = jest.spyOn(controller['logger'], 'log');
      mockWalletService.deleteWallet.mockResolvedValue({
        message: 'Wallet deleted successfully',
      });

      await controller.deleteWallet('01994ba2-3c6e-7b53-9660-0f766d409578', mockAuthenticatedRequest);

      expect(logSpy).toHaveBeenCalledWith(
        'Deleting wallet 01994ba2-3c6e-7b53-9660-0f766d409578 for user: 01994ba2-3c6e-7b53-9660-0f766d409577',
      );
    });

    it('should log errors on update failure', async () => {
      const errorSpy = jest.spyOn(controller['logger'], 'error');
      const error = new Error('Update failed');
      mockWalletService.updateWallet.mockRejectedValue(error);

      await expect(
        controller.updateWallet(
          '01994ba2-3c6e-7b53-9660-0f766d409578',
          { is_primary: true },
          mockAuthenticatedRequest,
        ),
      ).rejects.toThrow('Update failed');

      expect(errorSpy).toHaveBeenCalledWith(
        'Failed to update wallet 01994ba2-3c6e-7b53-9660-0f766d409578: Update failed',
        error.stack,
      );
    });

    it('should log errors on delete failure', async () => {
      const errorSpy = jest.spyOn(controller['logger'], 'error');
      const error = new Error('Delete failed');
      mockWalletService.deleteWallet.mockRejectedValue(error);

      await expect(
        controller.deleteWallet('01994ba2-3c6e-7b53-9660-0f766d409578', mockAuthenticatedRequest),
      ).rejects.toThrow('Delete failed');

      expect(errorSpy).toHaveBeenCalledWith(
        'Failed to delete wallet 01994ba2-3c6e-7b53-9660-0f766d409578: Delete failed',
        error.stack,
      );
    });
  });
});

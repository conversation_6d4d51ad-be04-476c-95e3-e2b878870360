import { BadRequestException } from '@nestjs/common';
import { WalletType, ChainSymbol } from '../../../generated/prisma';
import {
  WalletValidationResult,
  WalletCreationData,
} from '../interfaces/wallet.interface';
import {
  ADDRESS_VALIDATIONS,
  SUPPORTED_WALLET_TYPES,
  SUPPORTED_NETWORKS,
  WALLET_ERROR_MESSAGES,
} from '../constants/wallet.constants';

/**
 * Utility class for wallet validation operations
 */
export class WalletValidationUtil {
  /**
   * Validate wallet address format
   * @param address - Wallet address to validate
   * @param chainSymbol - Chain symbol for address format validation
   * @returns boolean indicating if address is valid
   */
  static validateWalletAddress(address: string, chainSymbol: ChainSymbol): boolean {
    if (!address || typeof address !== 'string') {
      return false;
    }

    // For now, we primarily support Ethereum-style addresses
    // This can be extended to support other formats based on chain symbol
    switch (chainSymbol) {
      case ChainSymbol.ETH:
      case ChainSymbol.BNB:
      case ChainSymbol.MATIC:
      case ChainSymbol.AVAX:
      case ChainSymbol.FTM:
      case ChainSymbol.ARB:
      case ChainSymbol.OP:
        return ADDRESS_VALIDATIONS.ETHEREUM.format.test(address);
      default:
        // For other chains, use Ethereum format as default
        return ADDRESS_VALIDATIONS.ETHEREUM.format.test(address);
    }
  }

  /**
   * Validate multiple chains for duplicates and compatibility
   * @param chains - Array of chain data to validate
   * @param walletType - Wallet type for compatibility check
   * @returns WalletValidationResult with validation details
   */
  static validateMultipleChains(
    chains: { chainId: string; chainName: string; symbol: ChainSymbol }[],
    walletType: WalletType,
  ): WalletValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check if chains array is provided and not empty
    if (!chains || !Array.isArray(chains) || chains.length === 0) {
      errors.push(WALLET_ERROR_MESSAGES.NO_CHAINS_PROVIDED);
      return { isValid: false, errors, warnings };
    }

    // Check maximum chains limit
    if (chains.length > 10) {
      errors.push(WALLET_ERROR_MESSAGES.TOO_MANY_CHAINS);
    }

    // Check for duplicate chains (by chainId or symbol)
    const chainIds = new Set<string>();
    const chainSymbols = new Set<ChainSymbol>();

    for (const chain of chains) {
      // Check for duplicate chain IDs
      if (chainIds.has(chain.chainId)) {
        errors.push(`Duplicate chain ID found: ${chain.chainId}`);
      } else {
        chainIds.add(chain.chainId);
      }

      // Check for duplicate chain symbols
      if (chainSymbols.has(chain.symbol)) {
        errors.push(`Duplicate chain symbol found: ${chain.symbol}`);
      } else {
        chainSymbols.add(chain.symbol);
      }

      // Validate individual chain symbol
      if (!this.validateChainSymbol(chain.symbol)) {
        errors.push(`Invalid chain symbol: ${chain.symbol}`);
      }

      // Validate chain compatibility with wallet type
      if (!this.isChainCompatibleWithWallet(chain.symbol, walletType)) {
        warnings.push(`Chain ${chain.symbol} may not be fully compatible with ${walletType}`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * Check if a chain is compatible with a wallet type
   * @param chainSymbol - Chain symbol to check
   * @param walletType - Wallet type to check compatibility with
   * @returns boolean indicating compatibility
   */
  static isChainCompatibleWithWallet(chainSymbol: ChainSymbol, walletType: WalletType): boolean {
    // Define compatibility matrix
    const compatibilityMatrix: Record<WalletType, ChainSymbol[]> = {
      [WalletType.METAMASK]: [
        ChainSymbol.ETH,
        ChainSymbol.BNB,
        ChainSymbol.MATIC,
        ChainSymbol.AVAX,
        ChainSymbol.FTM,
        ChainSymbol.ARB,
        ChainSymbol.OP,
      ],
      [WalletType.WALLET_CONNECT]: [
        ChainSymbol.ETH,
        ChainSymbol.BNB,
        ChainSymbol.MATIC,
        ChainSymbol.AVAX,
        ChainSymbol.FTM,
        ChainSymbol.ARB,
        ChainSymbol.OP,
      ],
      [WalletType.SAFE]: [ChainSymbol.ETH, ChainSymbol.MATIC, ChainSymbol.ARB, ChainSymbol.OP],
      [WalletType.EXTERNAL]: [
        ChainSymbol.ETH,
        ChainSymbol.BNB,
        ChainSymbol.MATIC,
        ChainSymbol.AVAX,
        ChainSymbol.FTM,
        ChainSymbol.ARB,
        ChainSymbol.OP,
      ],
      [WalletType.HARDWARE]: [
        ChainSymbol.ETH,
        ChainSymbol.BNB,
        ChainSymbol.MATIC,
        ChainSymbol.AVAX,
        ChainSymbol.FTM,
        ChainSymbol.ARB,
        ChainSymbol.OP,
      ],
    };

    return compatibilityMatrix[walletType]?.includes(chainSymbol) ?? false;
  }

  /**
   * Validate wallet type
   * @param walletType - Wallet type to validate
   * @returns boolean indicating if wallet type is valid
   */
  static validateWalletType(walletType: WalletType): boolean {
    return Object.values(WalletType).includes(walletType);
  }

  /**
   * Validate chain symbol
   * @param chainSymbol - Chain symbol to validate
   * @returns boolean indicating if chain symbol is valid
   */
  static validateChainSymbol(chainSymbol: ChainSymbol): boolean {
    return Object.values(ChainSymbol).includes(chainSymbol);
  }

  /**
   * Validate chain ID format
   * @param chainId - Chain ID to validate
   * @returns boolean indicating if chain ID is valid
   */
  static validateChainId(chainId: string): boolean {
    if (!chainId || typeof chainId !== 'string') {
      return false;
    }

    // Chain ID should be alphanumeric and between 1-50 characters
    return /^[a-zA-Z0-9-_]{1,50}$/.test(chainId);
  }

  /**
   * Validate chain name format
   * @param chainName - Chain name to validate
   * @returns boolean indicating if chain name is valid
   */
  static validateChainName(chainName: string): boolean {
    if (!chainName || typeof chainName !== 'string') {
      return false;
    }

    // Chain name should be between 1-100 characters and contain reasonable characters
    return /^[a-zA-Z0-9\s\-_.()]{1,100}$/.test(chainName);
  }

  /**
   * Comprehensive validation for wallet creation data with multiple chains
   * @param data - Wallet creation data to validate
   * @returns WalletValidationResult with validation status and errors
   */
  static validateWalletCreationData(data: WalletCreationData): WalletValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Validate wallet type
    if (!this.validateWalletType(data.walletType)) {
      errors.push(WALLET_ERROR_MESSAGES.INVALID_WALLET_TYPE);
    }

    // Validate each chain and wallet address compatibility
    for (const chain of data.chains) {
      // Validate wallet address against each chain
      if (!this.validateWalletAddress(data.walletAddress, chain.symbol)) {
        errors.push(`${WALLET_ERROR_MESSAGES.INVALID_WALLET_ADDRESS} for chain ${chain.symbol}`);
      }

      // Validate chain symbol
      if (!this.validateChainSymbol(chain.symbol)) {
        errors.push(`${WALLET_ERROR_MESSAGES.INVALID_CHAIN_SYMBOL}: ${chain.symbol}`);
      }

      // Validate chain ID
      if (!this.validateChainId(chain.chainId)) {
        errors.push(`Invalid chain ID format: ${chain.chainId}`);
      }

      // Validate chain name
      if (!this.validateChainName(chain.chainName)) {
        errors.push(`Invalid chain name format: ${chain.chainName}`);
      }

      // Check if wallet type is compatible with chain symbol
      if (!this.isWalletTypeCompatibleWithChain(data.walletType, chain.symbol)) {
        warnings.push(
          `Wallet type ${data.walletType} may not be fully compatible with ${chain.symbol} network`,
        );
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * Check if wallet type is compatible with chain symbol
   * @param walletType - Wallet type
   * @param chainSymbol - Chain symbol
   * @returns boolean indicating compatibility
   */
  static isWalletTypeCompatibleWithChain(
    walletType: WalletType,
    chainSymbol: ChainSymbol,
  ): boolean {
    // Define compatibility matrix
    const compatibility: Record<WalletType, ChainSymbol[]> = {
      [WalletType.METAMASK]: [
        ChainSymbol.ETH,
        ChainSymbol.BNB,
        ChainSymbol.MATIC,
        ChainSymbol.AVAX,
        ChainSymbol.FTM,
        ChainSymbol.ARB,
        ChainSymbol.OP,
      ],
      [WalletType.WALLET_CONNECT]: [
        ChainSymbol.ETH,
        ChainSymbol.BNB,
        ChainSymbol.MATIC,
        ChainSymbol.AVAX,
        ChainSymbol.FTM,
        ChainSymbol.ARB,
        ChainSymbol.OP,
      ],
      [WalletType.SAFE]: [
        ChainSymbol.ETH,
        ChainSymbol.BNB,
        ChainSymbol.MATIC,
        ChainSymbol.AVAX,
        ChainSymbol.ARB,
        ChainSymbol.OP,
      ],
      [WalletType.EXTERNAL]: Object.values(ChainSymbol), // External wallets support all chains
      [WalletType.HARDWARE]: Object.values(ChainSymbol), // Hardware wallets support most chains
    };

    return compatibility[walletType]?.includes(chainSymbol) ?? false;
  }

  /**
   * Validate user ID format
   * @param userId - User ID to validate
   * @throws BadRequestException if user ID is invalid
   */
  static validateUserId(userId: string): void {
    if (!userId || typeof userId !== 'string' || userId.trim().length === 0) {
      throw new BadRequestException(`${WALLET_ERROR_MESSAGES.INVALID_USER_ID}: User ID is empty or null`);
    }

    // More flexible UUID validation for UUIDv7 format used by Prisma uuid(7)
    // UUIDv7 format: xxxxxxxx-xxxx-7xxx-xxxx-xxxxxxxxxxxx
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(userId)) {
      throw new BadRequestException(
        `${WALLET_ERROR_MESSAGES.INVALID_USER_ID}: Invalid UUID format. Received: "${userId}" (length: ${userId.length})`
      );
    }

    // Check for proper UUID length
    if (userId.length !== 36) {
      throw new BadRequestException(
        `${WALLET_ERROR_MESSAGES.INVALID_USER_ID}: UUID must be 36 characters long. Received: "${userId}" (length: ${userId.length})`
      );
    }
  }

  /**
   * Validate wallet ID format
   * @param walletId - Wallet ID to validate
   * @throws BadRequestException if wallet ID is invalid
   */
  static validateWalletId(walletId: string): void {
    if (!walletId || typeof walletId !== 'string' || walletId.trim().length === 0) {
      throw new BadRequestException('Invalid wallet ID provided');
    }

    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(walletId)) {
      throw new BadRequestException('Invalid wallet ID provided');
    }
  }

  /**
   * Get supported address formats for a chain symbol
   * @param chainSymbol - Chain symbol
   * @returns Array of supported address validation patterns
   */
  static getSupportedAddressFormats(chainSymbol: ChainSymbol): RegExp[] {
    switch (chainSymbol) {
      case ChainSymbol.ETH:
      case ChainSymbol.BNB:
      case ChainSymbol.MATIC:
      case ChainSymbol.AVAX:
      case ChainSymbol.FTM:
      case ChainSymbol.ARB:
      case ChainSymbol.OP:
        return [ADDRESS_VALIDATIONS.ETHEREUM.format];
      default:
        return [ADDRESS_VALIDATIONS.ETHEREUM.format];
    }
  }

  /**
   * Get validation error message for address format
   * @param chainSymbol - Chain symbol
   * @returns Descriptive error message for address format
   */
  static getAddressFormatErrorMessage(chainSymbol: ChainSymbol): string {
    switch (chainSymbol) {
      case ChainSymbol.ETH:
      case ChainSymbol.BNB:
      case ChainSymbol.MATIC:
      case ChainSymbol.AVAX:
      case ChainSymbol.FTM:
      case ChainSymbol.ARB:
      case ChainSymbol.OP:
        return ADDRESS_VALIDATIONS.ETHEREUM.description;
      default:
        return 'Invalid address format';
    }
  }
}

import {
  IsString,
  IsNotEmpty,
  ValidateNested,
  Matches,
  Length,
  IsEnum,
  IsArray,
  ArrayMinSize,
  ArrayMaxSize,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { WalletType, ChainSymbol } from '../../../generated/prisma';

/**
 * DTO for chain information when creating a wallet
 */
export class ChainDto {
  @ApiProperty({
    description: 'Unique identifier for the blockchain network',
    example: '1',
    minLength: 1,
    maxLength: 50,
  })
  @IsString()
  @IsNotEmpty({ message: 'Chain ID is required' })
  @Length(1, 50, { message: 'Chain ID must be between 1 and 50 characters' })
  chainId: string;

  @ApiProperty({
    description: 'Human-readable name of the blockchain network',
    example: 'Ethereum Mainnet',
    minLength: 1,
    maxLength: 100,
  })
  @IsString()
  @IsNotEmpty({ message: 'Chain name is required' })
  @Length(1, 100, {
    message: 'Chain name must be between 1 and 100 characters',
  })
  chainName: string;

  @ApiProperty({
    description: 'Native token symbol for the blockchain network',
    enum: ChainSymbol,
    example: ChainSymbol.ETH,
  })
  @IsEnum(ChainSymbol, {
    message:
      'Invalid chain symbol. Must be one of: ETH, BNB, MATIC, AVAX, FTM, ARB, OP',
  })
  symbol: ChainSymbol;
}

/**
 * DTO for creating a new wallet
 */
export class CreateWalletDto {
  @ApiProperty({
    description: 'Blockchain wallet address (Ethereum format)',
    example: '******************************************',
    pattern: '^0x[a-fA-F0-9]{40}$',
  })
  @IsString()
  @IsNotEmpty({ message: 'Wallet address is required' })
  @Matches(/^0x[a-fA-F0-9]{40}$/, {
    message: 'Wallet address must be a valid Ethereum address format',
  })
  wallet_address: string;

  @ApiProperty({
    description: 'Type of wallet being connected',
    enum: WalletType,
    example: WalletType.METAMASK,
  })
  @IsEnum(WalletType, {
    message:
      'Invalid wallet type. Must be one of: METAMASK, WALLET_CONNECT, SAFE, EXTERNAL, HARDWARE',
  })
  wallet_type: WalletType;

  @ApiProperty({
    description: 'Chain information for the wallet (supports multiple chains)',
    type: [ChainDto],
    isArray: true,
    minItems: 1,
    maxItems: 10,
    example: [
      {
        chainId: '1',
        chainName: 'Ethereum Mainnet',
        symbol: 'ETH',
      },
      {
        chainId: '137',
        chainName: 'Polygon Mainnet',
        symbol: 'MATIC',
      },
    ],
  })
  @IsArray({ message: 'Chains must be an array' })
  @ArrayMinSize(1, { message: 'At least one chain is required' })
  @ArrayMaxSize(10, { message: 'Maximum of 10 chains allowed per wallet' })
  @ValidateNested({ each: true })
  @Type(() => ChainDto)
  chains: ChainDto[];
}

import {
  Controller,
  Post,
  Get,
  Param,
  Body,
  UseGuards,
  Request,
  HttpStatus,
  HttpCode,
  ValidationPipe,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiBody,
  ApiParam,
} from '@nestjs/swagger';
import { WalletService } from './wallet.service';
import { CreateWalletDto } from './dto/create-wallet.dto';
import {
  WalletResponseDto,
  WalletListResponseDto,
} from './dto/wallet-response.dto';
import { EnhancedJwtAuthGuard } from '../auth/guards/enhanced-jwt-auth.guard';
import { UserExtractionUtil } from '../kyc/utils/user-extraction.util';
import { AuthenticatedRequest } from '../kyc/interfaces/authenticated-user.interface';

@ApiTags('Wallet Management')
@Controller('wallets')
@UseGuards(EnhancedJwtAuthGuard)
@ApiBearerAuth('JWT')
export class WalletController {
  private readonly logger = new Logger(WalletController.name);

  constructor(private readonly walletService: WalletService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create a new wallet',
    description:
      'Creates a new blockchain wallet for the authenticated user. The first wallet created will automatically be set as the primary wallet. Wallet addresses must be unique across the system.',
  })
  @ApiBody({ type: CreateWalletDto })
  @ApiResponse({
    status: 201,
    description: 'Wallet successfully created',
    type: WalletResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid input data or user account not active',
    schema: {
      example: {
        statusCode: 400,
        message: ['Wallet address must be a valid Ethereum address format'],
        error: 'Bad Request',
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'User not authenticated',
    schema: {
      example: {
        statusCode: 401,
        message: 'Authentication failed',
        error: 'Unauthorized',
      },
    },
  })
  @ApiResponse({
    status: 409,
    description: 'Wallet address already exists',
    schema: {
      example: {
        statusCode: 409,
        message: 'Wallet address is already registered in the system',
        error: 'Conflict',
      },
    },
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
    schema: {
      example: {
        statusCode: 500,
        message: 'An unexpected error occurred in createWallet',
        error: 'Internal Server Error',
      },
    },
  })
  async createWallet(
    @Body(ValidationPipe) createWalletDto: CreateWalletDto,
    @Request() req: AuthenticatedRequest,
  ): Promise<WalletResponseDto> {
    try {
      // Extract user ID safely using utility
      const userId = UserExtractionUtil.extractUserId(req);

      this.logger.log(`Creating wallet for user: ${userId}`);

      return await this.walletService.createWallet(userId, createWalletDto);
    } catch (error) {
      this.logger.error(
        `Failed to create wallet: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  @Get()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get user wallets',
    description:
      'Retrieves all wallets belonging to the authenticated user. Results are ordered by primary status (primary first) and creation date (newest first).',
  })
  @ApiResponse({
    status: 200,
    description: 'Wallets retrieved successfully',
    type: [WalletListResponseDto],
  })
  @ApiResponse({
    status: 401,
    description: 'User not authenticated',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async getUserWallets(
    @Request() req: AuthenticatedRequest,
  ): Promise<WalletListResponseDto[]> {
    try {
      // Extract user ID safely using utility
      const userId = UserExtractionUtil.extractUserId(req);

      this.logger.log(`Fetching wallets for user: ${userId}`);

      return await this.walletService.getUserWallets(userId);
    } catch (error) {
      this.logger.error(
        `Failed to fetch user wallets: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  @Get(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get wallet by ID',
    description:
      'Retrieves detailed information about a specific wallet. Users can only access their own wallets.',
  })
  @ApiParam({
    name: 'id',
    description: 'Unique identifier of the wallet',
    example: 'f47ac10b-58cc-4372-a567-0e02b2c3d480',
  })
  @ApiResponse({
    status: 200,
    description: 'Wallet details retrieved successfully',
    type: WalletResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'User not authenticated',
  })
  @ApiResponse({
    status: 404,
    description: 'Wallet not found or access denied',
    schema: {
      example: {
        statusCode: 404,
        message: 'Wallet with ID f47ac10b-58cc-4372-a567-0e02b2c3d480 not found or access denied',
        error: 'Not Found',
      },
    },
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async getWalletById(
    @Param('id') walletId: string,
    @Request() req: AuthenticatedRequest,
  ): Promise<WalletResponseDto> {
    try {
      // Extract user ID safely using utility
      const userId = UserExtractionUtil.extractUserId(req);

      this.logger.log(`Fetching wallet ${walletId} for user: ${userId}`);

      return await this.walletService.getWalletById(walletId, userId);
    } catch (error) {
      this.logger.error(
        `Failed to fetch wallet ${walletId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}

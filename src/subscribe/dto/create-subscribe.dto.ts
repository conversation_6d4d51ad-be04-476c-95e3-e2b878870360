import { IsString, IsUUI<PERSON>, <PERSON>N<PERSON>Empty, IsDecimal } from 'class-validator';
import { Decimal } from '@prisma/client/runtime/library';
import { ApiProperty } from '@nestjs/swagger';

export class CreateSubscribeDto {
  @ApiProperty({
    description: 'Unique identifier of the product to subscribe to',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsUUID()
  @IsNotEmpty()
  productId: string;

  @ApiProperty({
    description: 'Unique identifier of the wallet to use for payment',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsUUID()
  @IsNotEmpty()
  walletId: string;

  @ApiProperty({
    description: 'Unique identifier of the blockchain chain to use',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsUUID()
  @IsNotEmpty()
  chainId: string;

  @ApiProperty({
    description: 'Payment provider to use for the subscription',
    example: 'stripe',
    enum: ['stripe', 'paypal', 'coinbase'],
  })
  @IsString()
  @IsNotEmpty()
  paymentProvider: string;

  @ApiProperty({
    description: 'Currency code for the payment',
    example: 'USD.eth',
  })
  @IsString()
  @IsNotEmpty()
  currencies: string;

  @ApiProperty({
    description: 'Subscription amount',
    example: '29.99',
    type: 'number',
  })
  @IsDecimal()
  amount: Decimal;
}

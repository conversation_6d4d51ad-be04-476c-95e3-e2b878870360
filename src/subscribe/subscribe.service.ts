// src/subscribe/services/subscribe.service.ts
import {
  Injectable,
  Logger,
  BadRequestException,
  NotFoundException,
  InternalServerErrorException,
} from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { CreateSubscribeDto } from '../dto/create-subscribe.dto';
import { SubscribeEntity } from '../interfaces/subscribe.interface';
import { Decimal } from 'prisma/client/runtime/library';

@Injectable()
export class SubscribeService {
  private readonly logger = new Logger(SubscribeService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * Create a new subscription
   * @param createSubscribeDto - Subscription creation data
   * @param userId - The ID of the user from token authorization
   * @returns Promise<SubscribeEntity> - Created subscription information
   */
  async create(
    createSubscribeDto: CreateSubscribeDto,
    userId: string,
  ): Promise<SubscribeEntity> {
    try {
      this.logger.log(`Creating subscription for user: ${userId}`);

      // Validate user exists
      await this.validateUserExists(userId);

      // Validate product exists
      await this.validateProductExists(createSubscribeDto.productId);

      // Validate wallet exists and belongs to user
      await this.validateWalletExists(
        createSubscribeDto.walletId,
        userId,
        createSubscribeDto.chainId,
      );

      // Create subscription in a transaction for data consistency
      return await this.prisma.$transaction(async (prisma) => {
        const subscription = await prisma.subscribe.create({
          data: {
            productId: createSubscribeDto.productId,
            userId: userId,
            walletId: createSubscribeDto.walletId,
            chainId: createSubscribeDto.chainId,
            paymentProvider: createSubscribeDto.paymentProvider,
            paymentStatus: 'UNPAID', // Set to UNPAID as per requirement
            checkoutStatus: 'PREPARED', // Set to PREPARED as per requirement
            subscribeStatus: 'ACTIVE', // Set to ACTIVE as per requirement
            amount: createSubscribeDto.amount,
            expectedPrice: new Decimal(0), // Set to 0 as per requirement
            currencies: createSubscribeDto.currencies,
          },
        });

        this.logger.log(
          `Successfully created subscription ${subscription.id} for user ${userId}`,
        );

        return subscription;
      });
    } catch (error) {
      return this.handleServiceError(error, 'create');
    }
  }

  /**
   * Validate that user exists in the system
   * @param userId - User ID to validate
   * @throws NotFoundException if user doesn't exist
   */
  private async validateUserExists(userId: string): Promise<void> {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { id: true },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }
  }

  /**
   * Validate that product exists in the system
   * @param productId - Product ID to validate
   * @throws NotFoundException if product doesn't exist
   */
  private async validateProductExists(productId: string): Promise<void> {
    const product = await this.prisma.product.findUnique({
      where: { id: productId },
      select: { id: true },
    });

    if (!product) {
      throw new NotFoundException('Product not found');
    }
  }

  /**
   * Validate that wallet exists and belongs to the user
   * @param walletId - Wallet ID to validate
   * @param userId - User ID to validate wallet ownership
   * @param chainId - Chain ID to validate
   * @throws NotFoundException if wallet doesn't exist or doesn't belong to user
   */
  private async validateWalletExists(
    walletId: string,
    userId: string,
    chainId: string,
  ): Promise<void> {
    const wallet = await this.prisma.wallet.findFirst({
      where: {
        id: walletId,
        userId: userId,
      },
      include: {
        chains: {
          where: {
            id: chainId,
          },
        },
      },
    });

    if (!wallet) {
      throw new NotFoundException(
        'Wallet not found or does not belong to user',
      );
    }

    if (!wallet.chains || wallet.chains.length === 0) {
      throw new NotFoundException('Chain not found for wallet');
    }
  }

  /**
   * Centralized error handling for service methods
   * @param error - The error that occurred
   * @param context - The context/method where the error occurred
   * @throws Appropriate NestJS exception based on error type
   */
  private handleServiceError(error: any, context: string): never {
    this.logger.error(
      `${context}: ${error.message}`,
      error.stack,
      SubscribeService.name,
    );

    // Re-throw known exceptions as-is
    if (
      error instanceof BadRequestException ||
      error instanceof NotFoundException
    ) {
      throw error;
    }

    // Handle Prisma-specific errors
    if (error.code) {
      switch (error.code) {
        case 'P2002':
          throw new BadRequestException(
            'A record with this data already exists',
          );
        case 'P2025':
          throw new NotFoundException('Record not found');
        case 'P2003':
          throw new BadRequestException('Invalid reference provided');
        default:
          this.logger.error(
            `Unhandled Prisma error code: ${error.code}`,
            error.meta,
          );
      }
    }

    // Default to internal server error
    throw new InternalServerErrorException(
      `An unexpected error occurred in ${context}`,
    );
  }
}

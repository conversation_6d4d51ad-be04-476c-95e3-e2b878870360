// src/subscribe/controllers/subscribe.controller.ts
import {
  Controller,
  Post,
  Body,
  UseGuards,
  Get,
  Param,
  Logger,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { UserInfo } from '../../auth/decorators/user-info.decorator';
import { UserInfoInterface } from '../../auth/interfaces/user-info.interface';
import { SubscribeService } from '../services/subscribe.service';
import { CreateSubscribeDto } from '../dto/create-subscribe.dto';
import { SubscribeEntity } from '../interfaces/subscribe.interface';

@Controller('subscribe')
export class SubscribeController {
  private readonly logger = new Logger(SubscribeController.name);

  constructor(private readonly subscribeService: SubscribeService) {}

  /**
   * Create a new subscription
   * @param createSubscribeDto - Subscription creation data
   * @param userInfo - User info from token authorization
   * @returns Promise<SubscribeEntity> - Created subscription information
   */
  @Post()
  @UseGuards(AuthGuard('jwt'))
  async create(
    @Body() createSubscribeDto: CreateSubscribeDto,
    @UserInfo() userInfo: UserInfoInterface,
  ): Promise<SubscribeEntity> {
    this.logger.log(`Creating subscription for user: ${userInfo.id}`);

    return await this.subscribeService.create(createSubscribeDto, userInfo.id);
  }
}

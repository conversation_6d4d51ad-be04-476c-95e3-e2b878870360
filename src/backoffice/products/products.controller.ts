import { Controller, Get, Param } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { ProductsService } from '../../products/products.service';
import { BaseResponseDto } from '../nav-management/dto/base-response.dto';
import { ProductResponseDto, ProductSummaryDto } from '../../products/dto';

// TODO TEMPORARY END POINT 
@ApiTags('Backoffice - Products (Temporary)')
@Controller('backoffice/products')
export class BackofficeProductsController {
  constructor(private readonly productsService: ProductsService) {}

  @Get(':id')
  @ApiOperation({
    summary: 'Get product by ID (detailed view) - TEMPORARY ENDPOINT',
    description:
      'Temporary endpoint without authorization. Get product by ID with detailed information.',
  })
  @ApiResponse({
    status: 200,
    description: 'Product retrieved successfully',
    type: BaseResponseDto,
    schema: {
      allOf: [
        { $ref: '#/components/schemas/BaseResponseDto' },
        {
          properties: {
            data: { $ref: '#/components/schemas/ProductResponseDto' },
          },
        },
      ],
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Product not found',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async findOne(
    @Param('id') id: string,
  ): Promise<BaseResponseDto<ProductResponseDto>> {
    return await this.productsService.findOne(id);
  }

  @Get()
  @ApiOperation({
    summary: 'Get all products (summary view) - TEMPORARY ENDPOINT',
    description:
      'Temporary endpoint without authorization. Get all products with summary information.',
  })
  @ApiResponse({
    status: 200,
    description: 'Products retrieved successfully',
    type: BaseResponseDto,
    schema: {
      allOf: [
        { $ref: '#/components/schemas/BaseResponseDto' },
        {
          properties: {
            data: {
              type: 'array',
              items: { $ref: '#/components/schemas/ProductSummaryDto' },
            },
          },
        },
      ],
    },
  })
  @ApiResponse({
    status: 404,
    description: 'No products found',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async findAll(): Promise<BaseResponseDto<ProductSummaryDto[]>> {
    return await this.productsService.findAll();
  }
}
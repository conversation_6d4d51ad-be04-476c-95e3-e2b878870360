import { Module } from '@nestjs/common';
import { BackofficeProductsController } from './products.controller';
import { ProductsService } from '../../products/products.service';
import { PrismaModule } from '../../prisma/prisma.module';

// TODO TEMPORARY END POINT 
@Module({
  imports: [PrismaModule],
  controllers: [BackofficeProductsController],
  providers: [ProductsService],
  exports: [ProductsService],
})
export class BackofficeProductsModule {}
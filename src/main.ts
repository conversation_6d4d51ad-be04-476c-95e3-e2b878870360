import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe } from './common/pipes/validation.pipe';
import { HttpExceptionFilter } from './common/filters/http-exception.filter';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { Logger } from '@nestjs/common';

async function bootstrap() {
  const logger = new Logger('Bootstrap');

  const app = await NestFactory.create(AppModule, {
    logger:
      process.env.NODE_ENV === 'production'
        ? ['error', 'warn', 'log']
        : ['error', 'warn', 'log', 'debug', 'verbose'],
  });

  // Enable graceful shutdown hooks
  app.enableShutdownHooks();

  app.useGlobalPipes(new ValidationPipe());
  app.useGlobalFilters(new HttpExceptionFilter());

  app.setGlobalPrefix('api/v1');

  // CORS configuration
  app.enableCors({
    origin:
      process.env.NODE_ENV === 'production'
        ? [
            'https://your-frontend-domain.com',
            'https://orbitum-app.fly.dev',
            'https://orbitum-backoffice.netlify.app'
          ]
        : true, // Allow all origins in development
    credentials: true,
  });

  // Swagger configuration - TERSEDIA DI SEMUA ENVIRONMENT
  const config = new DocumentBuilder()
    .setTitle('Orbitum API')
    .setDescription('User management and KYC/KYB API')
    .setVersion('1.0')
    .addTag('Users', 'User management endpoints')
    .addTag('Authentication', 'Authentication endpoints')
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'JWT',
        description: 'Enter JWT token',
        in: 'header',
      },
      'JWT-auth',
    )
    .addServer(
      process.env.NODE_ENV === 'production'
        ? 'https://orbitum-app.fly.dev'
        : 'http://localhost:3000',
      process.env.NODE_ENV === 'production'
        ? 'Production server'
        : 'Development server',
    )
    .build();

  const document = SwaggerModule.createDocument(app, config);

  // Setup Swagger dengan custom options
  SwaggerModule.setup('api/docs', app, document, {
    swaggerOptions: {
      persistAuthorization: true,
      tagsSorter: 'alpha',
      operationsSorter: 'alpha',
      docExpansion: 'none',
      filter: true,
      showRequestDuration: true,
    },
    customSiteTitle: 'Orbitum API Documentation',
    customfavIcon: '/favicon.ico',
    customJs: [
      'https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/4.15.5/swagger-ui-bundle.min.js',
      'https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/4.15.5/swagger-ui-standalone-preset.min.js',
    ],
    customCssUrl: [
      'https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/4.15.5/swagger-ui.min.css',
    ],
  });

  // Simple root endpoint for testing
  app.getHttpAdapter().get('/', (req, res) => {
    logger.log(`🔧 Root endpoint accessed from ${req.ip || 'unknown'}`);
    res.status(200).json({
      message: 'Orbitum API is running',
      timestamp: new Date().toISOString(),
      port: process.env.PORT || 3000,
    });
  });

  // Health check endpoint - optimized for Fly.io
  app.getHttpAdapter().get('/api/v1/health', async (req, res) => {
    const startTime = Date.now();
    const userAgent = req.get('User-Agent') || '';
    const isFlyHealthCheck = userAgent.includes('fly-health-check');

    // Reduce logging for health checks to avoid spam
    if (!isFlyHealthCheck) {
      logger.log(
        `🔧 Health check request received from ${req.ip || 'unknown'} at ${new Date().toISOString()}`,
      );
    }

    try {
      const healthData = {
        status: 'ok',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: process.env.NODE_ENV || 'development',
        version: '1.0.0',
        services: {
          api: 'running',
          scheduler: 'running',
        },
        memory: {
          used:
            Math.round(process.memoryUsage().heapUsed / 1024 / 1024) + ' MB',
          total:
            Math.round(process.memoryUsage().heapTotal / 1024 / 1024) + ' MB',
        },
        port: process.env.PORT || 3000,
        responseTime: Date.now() - startTime + 'ms',
      };

      // Only log detailed health check info for non-Fly health checks
      if (!isFlyHealthCheck) {
        logger.log(`🔧 Health check response: ${JSON.stringify(healthData)}`);
        logger.log(`🔧 Health check completed in ${Date.now() - startTime}ms`);
      }

      res.status(200).json(healthData);
    } catch (error) {
      logger.error(
        `🔧 Health check error after ${Date.now() - startTime}ms:`,
        error,
      );
      res.status(500).json({
        status: 'error',
        timestamp: new Date().toISOString(),
        error: error.message,
        responseTime: Date.now() - startTime + 'ms',
      });
    }
  });

  // Startup probe endpoint - lightweight check for initial startup
  app.getHttpAdapter().get('/api/v1/ready', (req, res) => {
    const startTime = Date.now();
    try {
      // Simple readiness check - just verify the app is running
      res.status(200).json({
        status: 'ready',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        responseTime: Date.now() - startTime + 'ms',
      });
    } catch (error) {
      res.status(503).json({
        status: 'not ready',
        timestamp: new Date().toISOString(),
        error: error.message,
        responseTime: Date.now() - startTime + 'ms',
      });
    }
  });

  // API Info endpoint
  app.getHttpAdapter().get('/api/v1/info', (req, res) => {
    res.json({
      name: 'Orbitum API',
      version: '1.0.0',
      description: 'User management and KYC/KYB API with integrated scheduler',
      environment: process.env.NODE_ENV || 'development',
      services: {
        api: 'running',
        scheduler: 'running',
      },
      endpoints: {
        health: `${req.protocol}://${req.get('host')}/api/v1/health`,
        ready: `${req.protocol}://${req.get('host')}/api/v1/ready`,
        docs: `${req.protocol}://${req.get('host')}/api/docs`,
        register: `${req.protocol}://${req.get('host')}/api/v1/users/register`,
      },
    });
  });

  const port = process.env.PORT || 3000;

  try {
    logger.log(`🔧 Starting application on port ${port}...`);
    logger.log(`🔧 Environment: ${process.env.NODE_ENV || 'development'}`);
    logger.log(`🔧 Process PID: ${process.pid}`);
    logger.log(
      `🔧 Memory usage: ${Math.round(process.memoryUsage().heapUsed / 1024 / 1024)} MB`,
    );

    await app.listen(port, '0.0.0.0');

    logger.log(`🚀 Application is running on port ${port}`);
    logger.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
    logger.log(`📚 Swagger docs: http://localhost:${port}/api/docs`);
    logger.log(`❤️ Health check: http://localhost:${port}/api/v1/health`);
    logger.log(`ℹ️ API info: http://localhost:${port}/api/v1/info`);
    logger.log(`⏰ Scheduler jobs are now running...`);

    // Test health endpoint immediately after startup
    logger.log(`🔧 Testing health endpoint...`);
    setTimeout(async () => {
      try {
        const http = await import('http');
        const options = {
          hostname: '0.0.0.0',
          port: port,
          path: '/api/v1/ready',
          method: 'GET',
          timeout: 3000,
        };

        const req = http.request(options, (res) => {
          logger.log(`🔧 Startup probe test response: ${res.statusCode}`);
        });

        req.on('error', (err) => {
          logger.error(`🔧 Startup probe test error: ${err.message}`);
        });

        req.end();
      } catch (error) {
        logger.error(`🔧 Startup probe test failed: ${error.message}`);
      }
    }, 1000);

    // Production-specific logs
    if (process.env.NODE_ENV === 'production') {
      logger.log(`🌐 Production URLs:`);
      logger.log(`   📚 Swagger: https://orbitum-app.fly.dev/api/docs`);
      logger.log(`   ❤️ Health: https://orbitum-app.fly.dev/api/v1/health`);
      logger.log(
        `   👤 Register: https://orbitum-app.fly.dev/api/v1/users/register`,
      );
    }

    // Setup graceful shutdown handlers
    setupGracefulShutdown(app, logger);
  } catch (error) {
    if (error.code === 'EADDRINUSE') {
      logger.error(`❌ Port ${port} is already in use`);
      logger.log(`💡 Try running: lsof -ti:${port} | xargs kill -9`);
    } else {
      logger.error('❌ Failed to start server:', error);
    }
    process.exit(1);
  }
}

// Graceful shutdown function
function setupGracefulShutdown(app: any, logger: Logger) {
  const gracefulShutdown = async (signal: string) => {
    logger.log(`🛑 ${signal} received, shutting down gracefully...`);

    try {
      // Close the NestJS application
      await app.close();
      logger.log('✅ Application closed successfully');

      // Exit the process
      process.exit(0);
    } catch (error) {
      logger.error('❌ Error during shutdown:', error);
      process.exit(1);
    }
  };

  // Handle different shutdown signals
  process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
  process.on('SIGINT', () => gracefulShutdown('SIGINT'));

  // Handle uncaught exceptions with restart capability
  process.on('uncaughtException', async (error) => {
    logger.error('❌ Uncaught Exception:', error);

    // Check if it's an OAuth-related error that should trigger restart
    if (isOAuthRelatedError(error)) {
      logger.log('🔄 OAuth-related error detected, attempting restart...');
      await attemptRestart('OAUTH_UNCAUGHT_EXCEPTION', error, logger);
    } else {
      gracefulShutdown('UNCAUGHT_EXCEPTION');
    }
  });

  // Handle unhandled promise rejections with restart capability
  process.on('unhandledRejection', async (reason, promise) => {
    logger.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);

    // Check if it's an OAuth-related error that should trigger restart
    if (isOAuthRelatedError(reason)) {
      logger.log('🔄 OAuth-related rejection detected, attempting restart...');
      await attemptRestart('OAUTH_UNHANDLED_REJECTION', reason, logger);
    } else {
      gracefulShutdown('UNHANDLED_REJECTION');
    }
  });

  logger.log('🔧 Graceful shutdown handlers registered');
}

// Helper function to detect OAuth-related errors
function isOAuthRelatedError(error: any): boolean {
  if (!error) return false;

  const errorString = error.toString().toLowerCase();
  const errorMessage = error.message?.toLowerCase() || '';
  const errorStack = error.stack?.toLowerCase() || '';

  const oauthKeywords = [
    'oauth',
    'google',
    'authentication',
    'auth/google',
    'frontend_url',
    'redirect',
    'callback',
    'passport',
  ];

  return oauthKeywords.some(
    (keyword) =>
      errorString.includes(keyword) ||
      errorMessage.includes(keyword) ||
      errorStack.includes(keyword),
  );
}

// Helper function to attempt restart with port cleanup
async function attemptRestart(
  reason: string,
  error: any,
  logger: Logger,
): Promise<void> {
  try {
    logger.log(`🔄 Attempting restart due to: ${reason}`);

    // Kill processes on current port
    const port = process.env.PORT || 3000;
    await killProcessesOnPort(Number(port), logger);

    // Wait a moment before restart
    await new Promise((resolve) => setTimeout(resolve, 2000));

    // Log restart attempt
    logger.log('🔄 Restarting application...');

    // Exit with code 0 to indicate restart (handled by process manager)
    process.exit(0);
  } catch (restartError) {
    logger.error('❌ Failed to restart application:', restartError);
    // Fall back to graceful shutdown
    process.exit(1);
  }
}

// Helper function to kill processes on port
async function killProcessesOnPort(
  port: number,
  logger: Logger,
): Promise<void> {
  try {
    logger.log(`🔧 Cleaning up processes on port ${port}...`);

    const { exec } = await import('child_process');
    const { promisify } = await import('util');
    const execAsync = promisify(exec);

    if (process.platform === 'win32') {
      // Windows
      try {
        await execAsync(`netstat -ano | findstr :${port}`);
        await execAsync(
          `for /f "tokens=5" %a in ('netstat -ano ^| findstr :${port}') do taskkill /PID %a /F`,
        );
      } catch {
        // Ignore errors - might mean no processes found
      }
    } else {
      // Unix/Linux/macOS
      try {
        const { stdout } = await execAsync(`lsof -ti:${port}`);
        if (stdout.trim()) {
          await execAsync(`lsof -ti:${port} | xargs kill -9`);
        }
      } catch {
        // Ignore errors - might mean no processes found
      }
    }

    logger.log(`✅ Port ${port} cleanup completed`);
  } catch (error) {
    logger.error(`❌ Failed to cleanup port ${port}:`, error.message);
  }
}

bootstrap().catch((error) => {
  const logger = new Logger('Bootstrap');
  logger.error('❌ Failed to start application:', error);
  process.exit(1);
});

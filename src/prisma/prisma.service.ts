import { Injectable, OnModuleInit, OnModule<PERSON><PERSON><PERSON>, <PERSON><PERSON> } from '@nestjs/common';
import { PrismaClient } from '../../generated/prisma';

@Injectable()
export class PrismaService extends PrismaClient implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(PrismaService.name);
  private connectionRetries = 0;
  private readonly maxRetries = 3;
  private readonly retryDelay = 2000; // 2 seconds

  constructor() {
    super({
      log: [
        { emit: 'event', level: 'query' },
        { emit: 'event', level: 'error' },
        { emit: 'event', level: 'info' },
        { emit: 'event', level: 'warn' },
      ],
      datasources: {
        db: {
          url: process.env.DATABASE_URL,
        },
      },
    });

    // Set up event listeners for better debugging
    this.$on('query', (e) => {
      if (process.env.NODE_ENV === 'development') {
        this.logger.debug(`Query: ${e.query} - Duration: ${e.duration}ms`);
      }
    });

    this.$on('error', (e) => {
      this.logger.error('Database error:', e);
    });

    this.$on('warn', (e) => {
      this.logger.warn('Database warning:', e);
    });

    this.$on('info', (e) => {
      this.logger.log('Database info:', e);
    });
  }

  async onModuleInit() {
    await this.connectWithRetry();
  }

  private async connectWithRetry(): Promise<void> {
    while (this.connectionRetries < this.maxRetries) {
      try {
        this.logger.log(`🔌 Attempting to connect to database... (Attempt ${this.connectionRetries + 1}/${this.maxRetries})`);

        // Test connection with timeout
        await Promise.race([
          this.$connect(),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Connection timeout')), 15000)
          )
        ]);

        // Verify connection with a simple query
        await this.$queryRaw`SELECT 1`;

        this.logger.log('✅ Database connected and verified successfully');
        this.connectionRetries = 0; // Reset retry counter on success
        return;
      } catch (error) {
        this.connectionRetries++;
        this.logger.error(`❌ Failed to connect to database (Attempt ${this.connectionRetries}/${this.maxRetries}):`, error.message);

        if (this.connectionRetries >= this.maxRetries) {
          this.logger.error('❌ Max connection retries reached. Database connection failed.');
          // Don't throw error during startup to allow health checks to work
          // The connection will be retried on first use
          break;
        }

        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, this.retryDelay));
      }
    }
  }

  async onModuleDestroy() {
    try {
      this.logger.log('🔌 Disconnecting from database...');
      await this.$disconnect();
      this.logger.log('✅ Database disconnected successfully');
    } catch (error) {
      this.logger.error('❌ Error disconnecting from database:', error);
    }
  }

  // Health check method
  async isHealthy(): Promise<boolean> {
    try {
      await this.$queryRaw`SELECT 1`;
      return true;
    } catch (error) {
      this.logger.error('Database health check failed:', error);
      return false;
    }
  }

  // Reconnect method for manual retry
  async reconnect(): Promise<void> {
    try {
      await this.$disconnect();
      this.connectionRetries = 0;
      await this.connectWithRetry();
    } catch (error) {
      this.logger.error('Failed to reconnect to database:', error);
      throw error;
    }
  }
}

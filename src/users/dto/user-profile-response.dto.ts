import { ApiProperty } from '@nestjs/swagger';
import { UserResponseDto, UserType, BusinessType, Status } from './user-response.dto';
import { WalletListResponseDto } from '../../wallet/dto/wallet-response.dto';

/**
 * Enhanced user profile response DTO that includes wallet information
 * Follows clean code principles with proper composition and TypeScript typing
 */
export class UserProfileResponseDto {
  @ApiProperty({ 
    description: 'Unique identifier for the user',
    example: 'f47ac10b-58cc-4372-a567-0e02b2c3d479'
  })
  id: string;

  @ApiProperty({ 
    description: 'First name of the user',
    example: 'John'
  })
  firstName: string;

  @ApiProperty({ 
    description: 'Last name of the user',
    example: 'Doe'
  })
  lastName: string;

  @ApiProperty({ 
    description: 'Email address of the user',
    example: '<EMAIL>'
  })
  email: string;

  @ApiProperty({ 
    description: 'Username of the user',
    example: 'johndo<PERSON>'
  })
  username: string;

  @ApiProperty({ 
    enum: UserType, 
    description: 'Type of the user',
    example: UserType.CONSUMER
  })
  type: UserType;

  @ApiProperty({
    enum: BusinessType,
    required: false,
    description: 'Business type for business users',
    example: BusinessType.RETAIL
  })
  businessType?: BusinessType;

  @ApiProperty({ 
    enum: Status, 
    description: 'Current status of the user',
    example: Status.ACTIVE
  })
  status: Status;

  @ApiProperty({ 
    description: 'Account creation timestamp',
    example: '2024-01-15T10:30:00.000Z'
  })
  createdAt: Date;

  @ApiProperty({ 
    description: 'Last update timestamp',
    example: '2024-01-15T10:30:00.000Z'
  })
  updatedAt: Date;

  @ApiProperty({
    description: 'Applicant ID for KYC/KYB processes',
    example: 'APP_123456789',
    required: false,
  })
  applicantId: string;

  @ApiProperty({
    type: [WalletListResponseDto],
    description: 'List of user wallets with basic information',
    example: [
      {
        id: 'f47ac10b-58cc-4372-a567-0e02b2c3d480',
        wallet_address: '******************************************',
        is_primary: true,
        status: 'ACTIVE',
        chain_name: 'Ethereum Mainnet',
        created_at: '2024-01-15T10:30:00.000Z'
      }
    ]
  })
  wallets: WalletListResponseDto[];

  /**
   * Constructor to create UserProfileResponseDto from user data and wallets
   * @param userData - User data from UserResponseDto
   * @param wallets - Array of user wallets
   */
  constructor(userData: UserResponseDto, wallets: WalletListResponseDto[] = []) {
    this.id = userData.id;
    this.firstName = userData.firstName;
    this.lastName = userData.lastName;
    this.email = userData.email;
    this.username = userData.username;
    this.type = userData.type;
    this.businessType = userData.businessType;
    this.status = userData.status;
    this.createdAt = userData.createdAt;
    this.updatedAt = userData.updatedAt;
    this.applicantId = userData.applicantId;
    this.wallets = wallets;
  }

  /**
   * Static factory method to create UserProfileResponseDto
   * Provides a clean way to construct the response with proper typing
   */
  static create(
    userData: UserResponseDto, 
    wallets: WalletListResponseDto[] = []
  ): UserProfileResponseDto {
    return new UserProfileResponseDto(userData, wallets);
  }
}

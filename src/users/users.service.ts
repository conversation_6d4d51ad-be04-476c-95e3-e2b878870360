import {
  Injectable,
  ConflictException,
  BadRequestException,
  InternalServerErrorException,
  NotFoundException,
  ForbiddenException,
  Logger,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateUserDto, UserType, BusinessType } from './dto/create-user.dto';
import { Status } from './dto/user-response.dto';
import { UserResponseDto } from './dto/user-response.dto';
import { UserProfileResponseDto } from './dto/user-profile-response.dto';
import { EnhancedUserProfileResponseDto } from './dto/enhanced-user-profile-response.dto';
import { WalletService } from '../wallet/wallet.service';
import * as bcrypt from 'bcrypt';
import * as crypto from 'crypto';
import { EmailService } from '../email/email.service';
import { ForgotPasswordDto } from './dto/forgot-password.dto';
import { PasswordResetResponseDto } from './dto/password-reset-response.dto';
import { CreateOAuthUserDto } from './dto/create-oauth-user.dto';

@Injectable()
export class UsersService {
  private readonly logger = new Logger(UsersService.name);
  private readonly SALT_ROUNDS = 12;
  private readonly TOKEN_EXPIRY_HOURS = 24;
  private readonly RESET_TOKEN_EXPIRY_HOURS = 1;

  constructor(
    private readonly prisma: PrismaService,
    private readonly emailService: EmailService,
    private readonly walletService: WalletService,
  ) {}

  async register(createUserDto: CreateUserDto): Promise<UserResponseDto> {
    try {
      this.validateBusinessUserRequirements(createUserDto);

      await this.checkUserExists(createUserDto.email, createUserDto.username);
      const hashedPassword = await this.hashPassword(createUserDto.password);

      const user = await this.createUser({
        ...createUserDto,
        password: hashedPassword,
      });

      const activationToken = await this.createActivationToken(user.id);

      await this.emailService.sendActivationEmail(
        user.email,
        user.username,
        activationToken.token,
        user.firstName,
      );

      // Map Prisma enums to DTO enums
      return new UserResponseDto({
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        username: user.username,
        type: user.type as UserType,
        businessType: user.businessType as BusinessType,
        status: user.status as Status,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        applicantId: user.applicantId || '', // Return empty string if null
        password: user.password,
      });
    } catch (error) {
      if (error.code === 'P2002') {
        throw new ConflictException('User already exists');
      }
      this.handleServiceError(error);
    }
  }

  async activateAccount(token: string): Promise<{ message: string }> {
    // Validate token format
    if (!token || typeof token !== 'string' || token.trim().length === 0) {
      throw new BadRequestException('Invalid activation token format');
    }

    // Decode the token in case it was URL encoded
    const decodedToken = decodeURIComponent(token.trim());

    try {
      const activationToken = await this.prisma.activationToken.findFirst({
        where: {
          token: decodedToken,
          expiresAt: {
            gte: new Date(),
          },
        },
        include: {
          user: true,
        },
      });

      if (!activationToken) {
        throw new BadRequestException('Invalid or expired activation token');
      }

      if (activationToken.user.status === 'ACTIVE') {
        // Clean up the token even if account is already active
        await this.prisma.activationToken
          .delete({
            where: { id: activationToken.id },
          })
          .catch(() => {
            // Ignore deletion errors for already active accounts
          });
        throw new BadRequestException('Account is already activated');
      }

      // Use transaction to ensure atomicity
      await this.prisma.$transaction(async (prisma) => {
        // Update user status
        await prisma.user.update({
          where: { id: activationToken.userId },
          data: { status: 'ACTIVE' },
        });

        // Delete the activation token
        await prisma.activationToken.delete({
          where: { id: activationToken.id },
        });

        // Clean up any other expired tokens for this user
        await prisma.activationToken.deleteMany({
          where: {
            userId: activationToken.userId,
            expiresAt: {
              lt: new Date(),
            },
          },
        });
      });

      return { message: 'Account activated successfully' };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      this.handleServiceError(error);
    }
  }

  async resendActivationEmail(email: string): Promise<{ message: string }> {
    // Validate email format
    if (!email || typeof email !== 'string' || !this.isValidEmail(email)) {
      throw new BadRequestException('Invalid email format');
    }

    try {
      const user = await this.findByEmail(email);
      if (!user) {
        throw new NotFoundException('User not found');
      }

      if (user.status === 'ACTIVE') {
        throw new BadRequestException('Account is already activated');
      }

      // Use transaction to ensure atomicity
      await this.prisma.$transaction(async (prisma) => {
        // Delete all existing activation tokens for this user
        await prisma.activationToken.deleteMany({
          where: { userId: user.id },
        });

        // Create new activation token
        const activationToken = await this.createActivationToken(user.id);

        // Send activation email
        await this.emailService.sendActivationEmail(
          user.email,
          user.username,
          activationToken.token,
          user.firstName,
        );
      });

      return { message: 'Activation email sent successfully' };
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      this.handleServiceError(error);
    }
  }

  private async createActivationToken(userId: string) {
    const token = crypto.randomBytes(32).toString('hex');
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + this.TOKEN_EXPIRY_HOURS);

    return this.prisma.activationToken.create({
      data: {
        token,
        expiresAt,
        userId,
      },
    });
  }

  async findByEmail(email: string) {
    return this.prisma.user.findUnique({
      where: { email: email.toLowerCase() },
    });
  }

  async findByUsername(username: string) {
    return this.prisma.user.findUnique({ where: { username: username } });
  }

  async findUserByEmailOrUsername(emailOrUsername: string) {
    const isEmail = this.isValidEmail(emailOrUsername);

    if (isEmail) {
      return this.findByEmail(emailOrUsername);
    }

    return this.findByUsername(emailOrUsername);
  }

  async requestPasswordReset(
    forgotPasswordDto: ForgotPasswordDto,
  ): Promise<PasswordResetResponseDto> {
    const { email } = forgotPasswordDto;

    // Validate email format
    if (!this.isValidEmail(email)) {
      throw new BadRequestException('Invalid email format');
    }

    try {
      const user = await this.findByEmail(email);
      if (!user) {
        throw new NotFoundException('User not found');
      }

      if (user.status !== 'ACTIVE') {
        throw new BadRequestException('Account is not active');
      }

      // Generate secure temporary password
      const temporaryPassword = this.generateSecurePassword();
      const hashedPassword = await this.hashPassword(temporaryPassword);

      // Use transaction to ensure atomicity
      await this.prisma.$transaction(async (prisma) => {
        // Delete any existing password reset tokens
        await prisma.passwordResetToken.deleteMany({
          where: { userId: user.id },
        });

        // Create new reset token
        const resetToken = await this.createPasswordResetToken(user.id);

        // Update user password (but keep it inactive until confirmed)
        await prisma.user.update({
          where: { id: user.id },
          data: { password: hashedPassword },
        });

        // Build confirmation URL
        const confirmationUrl = this.buildPasswordResetConfirmationUrl(
          resetToken.token,
        );

        // Send password reset email
        await this.emailService.sendPasswordResetEmail(
          user.email,
          user.username,
          temporaryPassword,
          confirmationUrl,
          user.firstName,
        );
      });

      return new PasswordResetResponseDto(
        'Password reset email sent successfully',
      );
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      this.handleServiceError(error);
    }
  }

  async confirmPasswordReset(token: string): Promise<{ message: string }> {
    // Validate token format
    if (!token || typeof token !== 'string' || token.trim().length === 0) {
      throw new BadRequestException('Invalid reset token format');
    }

    const decodedToken = decodeURIComponent(token.trim());

    try {
      const resetToken = await this.prisma.passwordResetToken.findFirst({
        where: {
          token: decodedToken,
          expiresAt: {
            gte: new Date(),
          },
        },
        include: {
          user: true,
        },
      });

      if (!resetToken) {
        throw new BadRequestException('Invalid or expired reset token');
      }

      // Use transaction to ensure atomicity
      await this.prisma.$transaction(async (prisma) => {
        // Delete the reset token
        await prisma.passwordResetToken.delete({
          where: { id: resetToken.id },
        });

        // Clean up any other expired tokens for this user
        await prisma.passwordResetToken.deleteMany({
          where: {
            userId: resetToken.userId,
            expiresAt: {
              lt: new Date(),
            },
          },
        });
      });

      return {
        message:
          'Password reset confirmed successfully. You can now login with your new password.',
      };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      this.handleServiceError(error);
    }
  }

  private validateBusinessUserRequirements(createUserDto: CreateUserDto): void {
    const { type, businessType } = createUserDto;

    // For CONSUMER users, businessType should be undefined or null
    // For BUSINESS users, businessType is optional
    if (
      type === UserType.CONSUMER &&
      businessType !== undefined &&
      businessType !== null
    ) {
      throw new BadRequestException(
        'Business type should not be provided for consumer users',
      );
    }
  }

  private async checkUserExists(
    email: string,
    username: string,
  ): Promise<void> {
    const [existingUserByEmail, existingUserByUsername] = await Promise.all([
      this.findByEmail(email),
      this.findByUsername(username),
    ]);

    if (existingUserByEmail || existingUserByUsername) {
      throw new ConflictException('User already exists');
    }
  }

  private async hashPassword(password: string): Promise<string> {
    try {
      return await bcrypt.hash(password, this.SALT_ROUNDS);
    } catch (error) {
      console.error('Failed to hash password:', error);
      throw new InternalServerErrorException('Failed to hash password');
    }
  }

  private async createUser(userData: CreateUserDto & { password: string }) {
    return this.prisma.user.create({
      data: {
        firstName: userData.firstName.trim(),
        lastName: userData.lastName.trim(),
        email: userData.email.toLowerCase(),
        username: userData.username,
        password: userData.password,
        type: userData.type,
        businessType: userData.businessType,
      },
    });
  }

  private handleServiceError(error: any): never {
    if (
      error instanceof ConflictException ||
      error instanceof BadRequestException
    ) {
      throw error;
    }

    console.error('UserService Error:', error);

    throw new InternalServerErrorException(
      'An error occurred while processing your request',
    );
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  private generateSecurePassword(): string {
    const length = 12;
    const charset =
      'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    let password = '';

    // Ensure at least one character from each category
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const numbers = '0123456789';
    const symbols = '!@#$%^&*';

    password += lowercase[Math.floor(Math.random() * lowercase.length)];
    password += uppercase[Math.floor(Math.random() * uppercase.length)];
    password += numbers[Math.floor(Math.random() * numbers.length)];
    password += symbols[Math.floor(Math.random() * symbols.length)];

    // Fill the rest randomly
    for (let i = 4; i < length; i++) {
      password += charset[Math.floor(Math.random() * charset.length)];
    }

    // Shuffle the password
    return password
      .split('')
      .sort(() => Math.random() - 0.5)
      .join('');
  }

  private async createPasswordResetToken(userId: string) {
    const token = crypto.randomBytes(32).toString('hex');
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + this.RESET_TOKEN_EXPIRY_HOURS);

    return this.prisma.passwordResetToken.create({
      data: {
        token,
        expiresAt,
        userId,
      },
    });
  }

  private buildPasswordResetConfirmationUrl(token: string): string {
    const baseUrl =
      process.env.BACKEND_URL ||
      (process.env.NODE_ENV === 'production'
        ? 'https://orbitum-app.fly.dev'
        : 'http://localhost:3000');

    const cleanBaseUrl = baseUrl.replace(/\/$/, '');
    const encodedToken = encodeURIComponent(token);
    return `${cleanBaseUrl}/api/v1/users/reset-password/${encodedToken}`;
  }

  // OAuth-related methods
  async findByGoogleId(googleId: string) {
    try {
      if (!googleId || typeof googleId !== 'string') {
        throw new BadRequestException('Invalid Google ID format');
      }

      return await this.prisma.user.findUnique({
        where: { googleId },
      });
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      console.error('Error finding user by Google ID:', error);
      throw new InternalServerErrorException(
        'Failed to find user by Google ID',
      );
    }
  }

  async linkGoogleAccount(
    userId: string,
    googleId: string,
    provider: string,
    accessToken: string,
  ) {
    try {
      if (!userId || !googleId || !provider || !accessToken) {
        throw new BadRequestException(
          'Missing required parameters for linking Google account',
        );
      }

      // Check if Google ID is already linked to another user
      const existingGoogleUser = await this.findByGoogleId(googleId);
      if (existingGoogleUser && existingGoogleUser.id !== userId) {
        throw new ConflictException(
          'Google account is already linked to another user',
        );
      }

      return await this.prisma.user.update({
        where: { id: userId },
        data: {
          googleId,
          oauthProvider: provider,
          oauthAccessToken: accessToken,
        },
      });
    } catch (error) {
      if (
        error instanceof BadRequestException ||
        error instanceof ConflictException
      ) {
        throw error;
      }
      console.error('Error linking Google account:', error);
      throw new InternalServerErrorException('Failed to link Google account');
    }
  }

  async createOAuthUser(userData: CreateOAuthUserDto): Promise<any> {
    try {
      // Validate required OAuth data
      if (
        !userData.googleId ||
        !userData.oauthProvider ||
        !userData.oauthAccessToken
      ) {
        throw new BadRequestException('Missing required OAuth data');
      }

      // Check if user already exists by email or Google ID
      const [existingUserByEmail, existingUserByGoogleId] = await Promise.all([
        this.findByEmail(userData.email),
        this.findByGoogleId(userData.googleId),
      ]);

      if (existingUserByEmail) {
        throw new ConflictException('User with this email already exists');
      }

      if (existingUserByGoogleId) {
        throw new ConflictException(
          'Google account is already linked to another user',
        );
      }

      // Ensure username is unique
      const uniqueUsername = await this.ensureUniqueUsername(userData.username);

      // Generate a random password for OAuth users (they won't use it for login)
      const randomPassword = this.generateSecurePassword();
      const hashedPassword = await this.hashPassword(randomPassword);

      // Create the user with OAuth data
      const user = await this.prisma.user.create({
        data: {
          firstName: userData.firstName.trim(),
          lastName: userData.lastName.trim(),
          email: userData.email.toLowerCase(),
          username: uniqueUsername,
          password: hashedPassword,
          type: userData.type,
          businessType: userData.businessType,
          googleId: userData.googleId,
          oauthProvider: userData.oauthProvider,
          oauthAccessToken: userData.oauthAccessToken,
          status: 'ACTIVE', // OAuth users are automatically activated
        },
      });

      return user;
    } catch (error) {
      if (
        error instanceof BadRequestException ||
        error instanceof ConflictException
      ) {
        throw error;
      }
      console.error('Error creating OAuth user:', error);
      throw new InternalServerErrorException('Failed to create OAuth user');
    }
  }

  async updateOAuthToken(userId: string, accessToken: string): Promise<void> {
    try {
      if (!userId || !accessToken) {
        throw new BadRequestException('User ID and access token are required');
      }

      await this.prisma.user.update({
        where: { id: userId },
        data: {
          oauthAccessToken: accessToken,
          updatedAt: new Date(),
        },
      });
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      console.error('Error updating OAuth token:', error);
      throw new InternalServerErrorException('Failed to update OAuth token');
    }
  }

  private async ensureUniqueUsername(baseUsername: string): Promise<string> {
    let username = baseUsername;
    let counter = 1;

    while (await this.findByUsername(username)) {
      username = `${baseUsername}${counter}`;
      counter++;
    }

    return username;
  }

  async getProfile(
    userId: string,
    userEmail: string,
  ): Promise<UserProfileResponseDto> {
    try {
      // Validate input parameters
      if (!userId || typeof userId !== 'string') {
        throw new BadRequestException('Invalid user ID');
      }

      if (
        !userEmail ||
        typeof userEmail !== 'string' ||
        !this.isValidEmail(userEmail)
      ) {
        throw new BadRequestException('Invalid user email');
      }

      // Find user by ID for security (primary lookup)
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
      });

      if (!user) {
        throw new NotFoundException('User not found');
      }

      // Additional security check: ensure the email matches
      if (user.email.toLowerCase() !== userEmail.toLowerCase()) {
        throw new ForbiddenException('Access denied');
      }

      // Check if account is active
      if (user.status !== 'ACTIVE') {
        throw new ForbiddenException('Account is not active');
      }

      // Create base user response
      const userResponse = new UserResponseDto({
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        username: user.username,
        type: user.type as UserType,
        businessType: user.businessType as BusinessType,
        status: user.status as Status,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        applicantId: user.applicantId || '', // Return empty string if null
        password: user.password, // This will be excluded by @Exclude() decorator
      });

      // Fetch user wallets with graceful error handling
      let userWallets = [];
      try {
        this.logger.log(`Fetching wallets for user profile: ${userId}`);
        userWallets = await this.walletService.getUserWallets(userId);
        this.logger.log(`Successfully fetched ${userWallets.length} wallets for user ${userId}`);
      } catch (walletError) {
        // Log wallet fetch error but don't fail the entire profile request
        this.logger.warn(
          `Failed to fetch wallets for user ${userId}: ${walletError.message}`,
          walletError.stack,
        );
        // userWallets remains empty array
      }

      // Return enhanced user profile with wallet information
      return UserProfileResponseDto.create(userResponse, userWallets);
    } catch (error) {
      if (
        error instanceof BadRequestException ||
        error instanceof NotFoundException ||
        error instanceof ForbiddenException
      ) {
        throw error;
      }

      this.logger.error('Error retrieving user profile:', error.stack);
      throw new InternalServerErrorException(
        'An error occurred while retrieving user profile',
      );
    }
  }

  /**
   * Get enhanced user profile with comprehensive wallet and blockchain chain data
   * @param userId - User ID from JWT token
   * @param userEmail - User email from JWT token for additional security
   * @returns Promise<EnhancedUserProfileResponseDto> - Enhanced user profile with detailed wallet and chain information
   */
  async getEnhancedProfile(
    userId: string,
    userEmail: string,
  ): Promise<EnhancedUserProfileResponseDto> {
    try {
      // Validate input parameters
      if (!userId || typeof userId !== 'string') {
        throw new BadRequestException('Invalid user ID');
      }

      if (
        !userEmail ||
        typeof userEmail !== 'string' ||
        !this.isValidEmail(userEmail)
      ) {
        throw new BadRequestException('Invalid user email');
      }

      // Find user by ID for security (primary lookup)
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
      });

      if (!user) {
        throw new NotFoundException('User not found');
      }

      // Additional security check: ensure the email matches
      if (user.email.toLowerCase() !== userEmail.toLowerCase()) {
        throw new ForbiddenException('Access denied');
      }

      // Check if account is active
      if (user.status !== 'ACTIVE') {
        throw new ForbiddenException('Account is not active');
      }

      // Create base user response
      const userResponse = new UserResponseDto({
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        username: user.username,
        type: user.type as UserType,
        businessType: user.businessType as BusinessType,
        status: user.status as Status,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        applicantId: user.applicantId || '', // Return empty string if null
        password: user.password, // This will be excluded by @Exclude() decorator
      });

      // Fetch detailed user wallets with graceful error handling
      let userWallets = [];
      try {
        this.logger.log(`Fetching detailed wallets for enhanced user profile: ${userId}`);
        userWallets = await this.walletService.getUserWalletsDetailed(userId);
        this.logger.log(`Successfully fetched ${userWallets.length} detailed wallets for user ${userId}`);
      } catch (walletError) {
        // Log wallet fetch error but don't fail the entire profile request
        this.logger.warn(
          `Failed to fetch detailed wallets for user ${userId}: ${walletError.message}`,
          walletError.stack,
        );
        // userWallets remains empty array
      }

      // Fetch supported chains with graceful error handling
      let supportedChains = [];
      try {
        this.logger.log('Fetching supported blockchain networks for enhanced profile');
        supportedChains = await this.walletService.getSupportedChains();
        this.logger.log(`Successfully fetched ${supportedChains.length} supported chains`);
      } catch (chainError) {
        // Log chain fetch error but don't fail the entire profile request
        this.logger.warn(
          `Failed to fetch supported chains: ${chainError.message}`,
          chainError.stack,
        );
        // supportedChains remains empty array
      }

      // Return enhanced user profile with comprehensive wallet and chain information
      return EnhancedUserProfileResponseDto.create(userResponse, userWallets, supportedChains);
    } catch (error) {
      if (
        error instanceof BadRequestException ||
        error instanceof NotFoundException ||
        error instanceof ForbiddenException
      ) {
        throw error;
      }

      this.logger.error('Error retrieving enhanced user profile:', error.stack);
      throw new InternalServerErrorException(
        'An error occurred while retrieving enhanced user profile',
      );
    }
  }
}

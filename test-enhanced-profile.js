// Simple test script to verify the enhanced profile endpoint
const axios = require('axios');

const BASE_URL = 'http://localhost:3001/api/v1';

async function testEnhancedProfile() {
  try {
    console.log('🧪 Testing Enhanced User Profile Endpoint');
    console.log('==========================================');
    
    // First, let's test the health endpoint to make sure the server is running
    console.log('1. Testing server health...');
    const healthResponse = await axios.get(`${BASE_URL}/health`);
    console.log('✅ Server is healthy:', healthResponse.status);
    
    // Test the enhanced profile endpoint (this will fail without authentication, but we can see the structure)
    console.log('\n2. Testing enhanced profile endpoint structure...');
    try {
      const profileResponse = await axios.get(`${BASE_URL}/users/profile`);
      console.log('✅ Profile response:', profileResponse.data);
    } catch (error) {
      if (error.response && error.response.status === 401) {
        console.log('✅ Expected 401 Unauthorized (authentication required)');
        console.log('📋 Response structure preview:', error.response.data);
      } else {
        console.log('❌ Unexpected error:', error.message);
      }
    }
    
    // Test the Swagger documentation to see if our new DTOs are properly documented
    console.log('\n3. Testing Swagger documentation...');
    try {
      const swaggerResponse = await axios.get('http://localhost:3001/api/docs-json');
      const swagger = swaggerResponse.data;
      
      // Check if our enhanced DTOs are in the schema
      const schemas = swagger.components?.schemas || {};
      
      console.log('📚 Available schemas:');
      Object.keys(schemas).forEach(schema => {
        if (schema.includes('Profile') || schema.includes('Wallet') || schema.includes('Chain')) {
          console.log(`  - ${schema}`);
        }
      });
      
      // Check for our specific enhanced DTOs
      if (schemas.EnhancedUserProfileResponseDto) {
        console.log('✅ EnhancedUserProfileResponseDto found in Swagger');
        console.log('📋 Properties:', Object.keys(schemas.EnhancedUserProfileResponseDto.properties || {}));
      }
      
      if (schemas.SupportedChainDto) {
        console.log('✅ SupportedChainDto found in Swagger');
        console.log('📋 Properties:', Object.keys(schemas.SupportedChainDto.properties || {}));
      }
      
    } catch (error) {
      console.log('❌ Error accessing Swagger docs:', error.message);
    }
    
    console.log('\n🎉 Enhanced profile endpoint test completed!');
    console.log('\nNext steps:');
    console.log('1. Authenticate a user to test the full response');
    console.log('2. Create wallet data to see the enhanced wallet information');
    console.log('3. Verify supported chains are returned correctly');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testEnhancedProfile();
